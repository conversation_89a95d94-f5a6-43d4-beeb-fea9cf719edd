{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\boundary\\BoundaryDetailDialog.vue?vue&type=style&index=0&id=28aed4c0&scoped=true&lang=scss", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\boundary\\BoundaryDetailDialog.vue", "mtime": 1754969114219}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICJAL2Fzc2V0cy9zdHlsZXMvY3VzdG9tRm9ybSI7Cjo6di1kZWVwIC5lbC1kaWFsb2dfX2JvZHkgewogIHBhZGRpbmc6IDAgMCAyMHB4IDAgIWltcG9ydGFudDsKfQo="}, {"version": 3, "sources": ["BoundaryDetailDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiKA;AACA;AACA;AACA", "file": "BoundaryDetailDialog.vue", "sourceRoot": "src/views/safe/boundary", "sourcesContent": ["<template>\n  <el-dialog :visible.sync=\"visible\" :title=\"title\" width=\"50%\">\n    <div class=\"customForm-container\" style=\"padding: 0 20px\">\n      <div class=\"my-title\"><img src=\"@/assets/images/application/jbxx.png\" alt=\"\">基本信息</div>\n      <el-descriptions\n        class=\"custom-column\"\n        direction=\"vertical\"\n        size=\"medium\"\n        :colon=\"false\"\n        label-class-name=\"custom-label-style\"\n        content-class-name=\"custom-content-style\"\n        :column=\"2\"\n      >\n        <template v-for=\"item in visibleAssetFields\">\n          <el-descriptions-item :key=\"item.fieldKey\" :label=\"item.fieldName\">\n            <!-- 字典字段特殊处理 -->\n            <template v-if=\"dictFields.includes(item.fieldKey)\">\n              <span\n                v-for=\"dict in getDictData(item.fieldKey)\"\n                :key=\"dict.value\"\n                v-if=\"dict.value === form[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <template v-if=\"item.fieldKey === 'deptId'\">\n              <span\n                v-for=\"dict in deptOptions\"\n                :key=\"dict.id\"\n                v-if=\"dict.id === form[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <!-- 普通字段直接显示 -->\n            <template v-else>\n              {{ getFieldValue(item.fieldKey) }}\n            </template>\n          </el-descriptions-item>\n        </template>\n      </el-descriptions>\n\n      <div class=\"my-title\" style=\"margin-top: 20px;\">\n        <i class=\"el-icon-office-building icon\" />\n        区域内资产\n      </div>\n      <asset-over-view-table\n        style=\"width: 100%;\n              padding-right: 10px;\"\n        :disabled=\"!editable\"\n        :if-reflush=\"dialogVisible\"\n        :asset-overview-query=\"queryParamsBoundaryAsset\"\n        :asset-class=\"[2, 3, 5, 9]\"\n        :asset-find-fun=\"getBoundaryAssetsList\"\n        :asset-add-fun=\"handleAddAsset\"\n        :asset-delete-fun=\"handleDeleteAsset\"\n      />\n\n      <div class=\"my-title\" style=\"margin-top: 20px;\">\n        <i class=\"el-icon-files icon\" />\n        相关文件\n      </div>\n      <asset-file style=\"width: 100%; padding-right: 10px;\"\n                  :disabled=\"!editable\"\n                  :asset-id=\"form.assetId\"\n      />\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport assetOverViewTable from \"@/views/components/table/assetOverViewTable.vue\";\nimport AssetFile from \"@/views/dimension/file/index.vue\";\nimport {deptTreeSelect, getAllDeptTree} from \"@/api/system/user\";\n\nexport default {\n  name: \"BoundaryDetailDialog\",\n  components: {AssetFile, assetOverViewTable},\n  props: {\n    boundaryDetailVisible: {\n      type: Boolean,\n      default: false\n    },\n    title: {\n      type: String,\n      default: '查看边界区域'\n    },\n    form: Object,\n    editable: Boolean,\n    editItem: String,\n    children: Array,\n    queryParamsBoundaryAsset: Object,\n    dictOptions: Array,\n    getBoundaryAssetsList: Function,\n    handleAddAsset: Function,\n    handleDeleteAsset: Function,\n    assetList: {\n      type: Array,\n      default: function () {\n        return []\n      }\n    }\n  },\n  data() {\n    return {\n      dialogVisible: this.visible,\n      deptOptions: []\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.boundaryDetailVisible;\n      },\n      set(val) {\n        this.$emit('update:boundaryDetailVisible', val);\n      }\n    },\n\n    visibleAssetFields() {\n      return this.assetList.filter(item => item.isShow);\n    },\n\n    dictFields() {\n      return ['degreeImportance'];\n    }\n  },\n  created() {\n    this.getDeptTree();\n  },\n  methods: {\n    getDictData(fieldKey) {\n      switch (fieldKey) {\n        case 'degreeImportance':\n          return this.dictOptions;\n        default:\n          return [];\n      }\n    },\n    /** 查询所属部门 */\n    getDeptTree() {\n      getAllDeptTree().then(response => {\n        this.deptOptions = response.data;\n      });\n    },\n    getFieldValue(field) {\n      if(this.dictFields.includes(field)){\n        return '';\n      }\n      if (field === 'assetType') {\n        return this.form.assetTypeDesc || this.form[field];\n      }\n      return this.form[field];\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n::v-deep .el-dialog__body {\n  padding: 0 0 20px 0 !important;\n}\n</style>\n"]}]}
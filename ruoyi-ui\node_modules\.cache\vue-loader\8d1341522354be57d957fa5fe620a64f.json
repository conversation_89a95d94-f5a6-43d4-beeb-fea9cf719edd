{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\basicInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\basicInfo.vue", "mtime": 1754969114240}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBhc3NldFJlZ2lzdGVyIGZyb20gIkAvbWl4aW5zL2Fzc2V0UmVnaXN0ZXIiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0Jhc2ljSW5mbycsCiAgbWl4aW5zOiBbYXNzZXRSZWdpc3Rlcl0sCiAgZGljdHM6IFsnaW1wdF9ncmFkZScsICdzeXNfeWVzX25vJywgJ3Byb2NfdHlwZScsJ2lzX3NwYXJpbmcnXSwKICBwcm9wczogewogICAgZm9ybTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0sCiAgICBlZHJGb3JtOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogbnVsbAogICAgfSwKICAgIG15VGFnczogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogbnVsbAogICAgfSwKICAgIG1hY0FpcExpc3Q6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhY3RpdmVOYW1lczogWycxJywgJzInLCAnMycsICc0J10sCiAgICAgIGNvbGxhcHNlTGFiZWxTcGFuOiA0LAogICAgICBjb2xsYXBzZUNvbnRlbnRTcGFuOiA4LAogICAgICBoZWFkZXJDZWxsU3R5bGU6IHsgJ2ZvbnQtd2VpZ2h0JzogJ25vcm1hbCcsIGNvbG9yOiAnIzk3OTc5NycgfSwKICAgICAgY2VsbFN0eWxlOiB7ICdmb250LXdlaWdodCc6ICdib2xkJyB9LAogICAgICBhc3NldEFsbG9jYXRpb25UeXBlOiAnMicsCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgdmlzaWJsZUFzc2V0RmllbGRzKCkgewogICAgICByZXR1cm4gKHRoaXMuYXNzZXRMaXN0IHx8IFtdKS5tYXAoZ3JvdXAgPT4gKHsKICAgICAgICAuLi5ncm91cCwKICAgICAgICBmaWVsZHNJdGVtczogZ3JvdXAuZmllbGRzSXRlbXMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pc1Nob3cpCiAgICAgIH0pKTsKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICBnZXRGaWVsZFZhbHVlKGZpZWxkKSB7CiAgICAgIHJldHVybiB0aGlzLmZvcm1bZmllbGQuZmllbGRLZXldOwogICAgfSwKICAgIGdldEZpZWxkRGlzcGxheVZhbHVlKGZpZWxkKSB7CiAgICAgIGNvbnN0IHZhbHVlID0gdGhpcy5nZXRGaWVsZFZhbHVlKGZpZWxkKTsKICAgICAgLy8g54m55q6K5a2X5q615aSE55CGCiAgICAgIGlmIChmaWVsZC5maWVsZEtleSA9PT0gJ2RlZ3JlZUltcG9ydGFuY2UnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZGljdC50eXBlLmltcHRfZ3JhZGUuZmluZChkID0+IGQudmFsdWUgPT09IHZhbHVlKT8ubGFiZWwgfHwgdmFsdWU7CiAgICAgIH0KICAgICAgaWYgKGZpZWxkLmZpZWxkS2V5ID09PSAnaXNWaXJ0dWFsJykgewogICAgICAgIHJldHVybiB2YWx1ZSA9PT0gJ1knID8gJ+aYrycgOiB2YWx1ZSA9PT0gJ04nID8gJ+WQpicgOiB2YWx1ZTsKICAgICAgfQogICAgICBpZiAoZmllbGQuZmllbGRLZXkgPT09ICdpc1NwYXJpbmcnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZGljdC50eXBlLmlzX3NwYXJpbmcuZmluZChkID0+IGQudmFsdWUgPT09IHZhbHVlKT8ubGFiZWwgfHwgdmFsdWU7CiAgICAgIH0KICAgICAgaWYgKGZpZWxkLmZpZWxkS2V5ID09PSAnbWFpbnRhaW5Vbml0JykgewogICAgICAgIHJldHVybiB0aGlzLmZvcm0ubWFpbnRhaW5Vbml0TmFtZTsKICAgICAgfQogICAgICBpZiAoZmllbGQuZmllbGRLZXkgPT09ICdmYWNpbGl0eU1hbnVmYWN0dXJlcicpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLmZhY2lsaXR5TWFudWZhY3R1cmVyTmFtZSB8fCB2YWx1ZTsKICAgICAgfQogICAgICBpZiAoZmllbGQuZmllbGRLZXkgPT09ICd2ZW5kb3InKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS52ZW5kb3JOYW1lIHx8IHZhbHVlOwogICAgICB9CiAgICAgIGlmIChmaWVsZC5maWVsZEtleSA9PT0gJ2Fzc2V0VHlwZScpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLmFzc2V0VHlwZURlc2MgfHwgdmFsdWU7CiAgICAgIH0KICAgICAgcmV0dXJuIHZhbHVlOwogICAgfQogIH0sCn0K"}, {"version": 3, "sources": ["basicInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "basicInfo.vue", "sourceRoot": "src/views/safe/server", "sourcesContent": ["<template>\n  <div class=\"customForm-container\" style=\"height: 100%; overflow: hidden\">\n    <template v-for=\"group in visibleAssetFields\">\n      <div :key=\"group.formName\">\n        <div class=\"my-title\">\n          <!-- 动态图标渲染 -->\n          <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/jbxx.png\" alt=\"\"/>\n          <i v-else-if=\"group.formName === '硬件/软件概况信息'\" class=\"el-icon-cpu\" style=\"font-size: 24px; margin: 0 5px; color: #4382fd\" />\n          <i v-else-if=\"group.formName === '位置信息'\" class=\"el-icon-location-information\" style=\"font-size: 24px; margin: 0 5px; color: #4382fd\" />\n          <img v-else-if=\"group.formName === '网络信息'\" src=\"@/assets/images/application/netinfo.png\" alt=\"\"/>\n          {{ group.formName }}\n        </div>\n\n        <!-- 网络信息特殊处理 -->\n        <template v-if=\"group.formName === '网络信息'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item label=\"外网IP\">\n              <el-col :class=\"[ !form.exposedIp ? 'empty' : 'collapse-value' ]\">\n                {{ form.exposedIp || '（空）' }}\n              </el-col>\n            </el-descriptions-item>\n          </el-descriptions>\n          <el-table :data=\"macAipList\" :header-cell-style=\"headerCellStyle\" :cell-style=\"cellStyle\">\n            <el-table-column label=\"是否主ip\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.mainIp === '1'\">是</span>\n                <span v-if=\"scope.row.mainIp === '0'\">否</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"domainFullName\" label=\"所属网络\" />\n            <el-table-column prop=\"ipv4\" label=\"ip\" />\n            <el-table-column prop=\"mac\" label=\"mac\" />\n          </el-table>\n        </template>\n\n        <!-- 其他分组正常渲染 -->\n        <template v-else>\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\">\n              <!-- 所属部门特殊处理 -->\n              <template v-if=\"field.fieldKey === 'deptId'\">\n                <span :class=\"[ !form.deptName ? 'empty' : 'collapse-value' ]\">\n                  {{ form.deptName || '（空）' }}\n                </span>\n              </template>\n\n              <!-- 其他字段正常渲染 -->\n              <template v-else>\n                <span :class=\"[ !getFieldValue(field) ? 'empty' : 'collapse-value' ]\">\n                  {{ getFieldDisplayValue(field) || '（空）' }}\n                </span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport assetRegister from \"@/mixins/assetRegister\";\nexport default {\n  name: 'BasicInfo',\n  mixins: [assetRegister],\n  dicts: ['impt_grade', 'sys_yes_no', 'proc_type','is_sparing'],\n  props: {\n    form: {\n      type: Object,\n      default: null\n    },\n    edrForm: {\n      type: Object,\n      default: null\n    },\n    myTags: {\n      type: Array,\n      default: null\n    },\n    macAipList: {\n      type: Array,\n      default: null\n    }\n  },\n  data() {\n    return {\n      activeNames: ['1', '2', '3', '4'],\n      collapseLabelSpan: 4,\n      collapseContentSpan: 8,\n      headerCellStyle: { 'font-weight': 'normal', color: '#979797' },\n      cellStyle: { 'font-weight': 'bold' },\n      assetAllocationType: '2',\n    }\n  },\n  computed: {\n    visibleAssetFields() {\n      return (this.assetList || []).map(group => ({\n        ...group,\n        fieldsItems: group.fieldsItems.filter(item => item.isShow)\n      }));\n    }\n  },\n  mounted() {},\n  methods: {\n    getFieldValue(field) {\n      return this.form[field.fieldKey];\n    },\n    getFieldDisplayValue(field) {\n      const value = this.getFieldValue(field);\n      // 特殊字段处理\n      if (field.fieldKey === 'degreeImportance') {\n        return this.dict.type.impt_grade.find(d => d.value === value)?.label || value;\n      }\n      if (field.fieldKey === 'isVirtual') {\n        return value === 'Y' ? '是' : value === 'N' ? '否' : value;\n      }\n      if (field.fieldKey === 'isSparing') {\n        return this.dict.type.is_sparing.find(d => d.value === value)?.label || value;\n      }\n      if (field.fieldKey === 'maintainUnit') {\n        return this.form.maintainUnitName;\n      }\n      if (field.fieldKey === 'facilityManufacturer') {\n        return this.form.facilityManufacturerName || value;\n      }\n      if (field.fieldKey === 'vendor') {\n        return this.form.vendorName || value;\n      }\n      if (field.fieldKey === 'assetType') {\n        return this.form.assetTypeDesc || value;\n      }\n      return value;\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n  .collapse-title {\n    font-weight: bold;\n    margin-left: 20px;\n  }\n  .collapse-row {\n    line-height: 32px;\n  }\n  .collapse-content-div {\n    margin: 0 20px;\n  }\n  .collapse-label {\n    color: #979797;\n  }\n  .collapse-value {\n    font-weight: bold;\n  }\n  .empty {\n    color: #979797;\n  }\n</style>\n"]}]}
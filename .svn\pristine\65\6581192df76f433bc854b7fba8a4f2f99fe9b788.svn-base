<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.monitor2.mapper.MonitorBssWebvulnDealMapper">

    <resultMap type="MonitorBssWebvulnDeal" id="MonitorBssWebvulnDealBaseResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="category"    column="category"    />
        <result property="severity"    column="severity"    />
        <result property="webUrl"    column="web_url"    />
        <result property="assetId"    column="asset_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="handleState"    column="handle_state"    />
        <result property="handleDesc"    column="handle_desc"    />
        <result property="scanNum"    column="scan_num"    />
        <result property="dataSource"    column="data_source"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="flowState"    column="flow_state"    />
        <result property="workId" column="workId" />
        <result property="prodId" column="prod_id" />
        <result property="assetName" column="asset_name" />
        <result property="deptName" column="dept_name" />
        <result property="disposer" column="disposer" />
        <result property="synchronizationStatus" column="synchronization_status" />
    </resultMap>

    <resultMap type="MonitorBssWebvulnDeal" id="MonitorBssWebvulnDealResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="category"    column="category"    />
        <result property="severity"    column="severity"    />
        <result property="webUrl"    column="web_url"    />
        <result property="assetId"    column="asset_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="handleState"    column="handle_state"    />
        <result property="handleDesc"    column="handle_desc"    />
        <result property="scanNum"    column="scan_num"    />
        <result property="dataSource"    column="data_source"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="flowState"    column="flow_state"    />
        <result property="workId" column="workId" />
        <result property="prodId" column="prod_id" />
        <result property="assetName" column="asset_name" />
        <result property="deptName" column="dept_name" />
        <result property="disposer" column="disposer" />
        <result property="synchronizationStatus" column="synchronization_status" />
        <result property="evidence" column="evidence" />
        <association property="bssVulnInfo" javaType="MonitorBssVulnInfo">
            <result property="title"    column="vi_title"    />
            <result property="poc"    column="vi_poc"    />
            <result property="exp"    column="vi_exp"    />
            <result property="exposures"    column="vi_exposures"    />
            <result property="summary"    column="vi_summary"    />
            <result property="impact"    column="vi_impact"    />
            <result property="solution"    column="vi_solution"    />
            <result property="definiteness"    column="vi_definiteness"    />
            <result property="category"    column="vi_category"    />
            <result property="severity"    column="vi_severity"    />
            <result property="cvss"    column="vi_cvss"    />
            <result property="publishedDateTime"    column="vi_published_date_time"    />
            <result property="detail"    column="vi_detail"    />
        </association>
    </resultMap>

    <sql id="selectMonitorBssWebvulnDealVo">
        select id, title, category, severity, web_url, asset_id, dept_id, scan_num, data_source, file_url, create_by, create_time, update_by, update_time from monitor_bss_webvuln_deal
    </sql>

    <select id="selectListCount" resultType="java.lang.Integer">
        select count(*) from (

        SELECT wvd.id FROM monitor_bss_webvuln_deal wvd
        LEFT JOIN tbl_work_order wo ON FIND_IN_SET(wvd.id,wo.event_ids) AND wo.work_type = '2'
        left join tbl_business_application tba on wvd.web_url = tba.url
        LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tba.domain_id
        left join sys_dept sd on sd.dept_id = tba.dept_id
        <where>
            wvd.severity not in (0)
            <if test="domainId != null  and domainId != ''"> and tnd.domain_id = #{domainId}</if>
            <if test="id != null  and id != ''"> and wvd.id = #{id}</if>
            <if test="disposers != null and disposers.size() > 0">
                and wvd.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and wvd.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="ids != null and ids.size()>0">
                and wvd.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and wvd.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and wvd.web_url in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="title != null  and title != ''"> and title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="severity != null"> and severity = #{severity}</if>
            <if test="webUrl != null  and webUrl != ''"> and web_url like concat('%',#{webUrl},'%')</if>
            <if test="dataSource != null "> and data_source = #{dataSource}</if>
            <if test="scanNum != null "> and scan_num = #{scanNum}</if>
            <if test="flowState != null and flowState != '' and flowState != 99"> and wo.flow_state = #{flowState}</if>
            <if test="flowState == 99"> and wo.flow_state is null</if>
            <if test="handleState != null "> and wvd.handle_state = #{handleState}</if>
            <if test="createTime != null">and DATE_FORMAT(wvd.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="deptId != null">and (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="applicationId != null">and tba.asset_id=#{applicationId}</if>
            <if test="startTime != null"> and wvd.create_time >= #{startTime} </if>
            <if test="endTime != null"> and wvd.create_time &lt;= #{endTime} </if>
        </where>
        GROUP BY wvd.id
                             ) temp
    </select>

    <select id="selectMonitorBssWebvulnDealList" parameterType="MonitorBssWebvulnDeal" resultMap="MonitorBssWebvulnDealResult">
        SELECT wvd.*,wo.flow_state,wo.id AS workId,wo.prod_id, GROUP_CONCAT(tba.asset_name) asset_name,GROUP_CONCAT(sd.dept_name) dept_name,fwvr.evidence FROM monitor_bss_webvuln_deal wvd
        LEFT JOIN tbl_work_order wo ON FIND_IN_SET(wvd.id,wo.event_ids) AND wo.work_type = '2'
        left join tbl_business_application tba on wvd.web_url = tba.url
        LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tba.domain_id
        left join sys_dept sd on sd.dept_id = tba.dept_id
        left join ffsafe_webscan_vulnresult fwvr on fwvr.vuln_name = wvd.title and fwvr.url = wvd.web_url
        <where>
            wvd.severity not in (0)
            <if test="domainId != null  and domainId != ''"> and tnd.domain_id = #{domainId}</if>
            <if test="id != null  and id != ''"> and wvd.id = #{id}</if>
            <if test="disposers != null and disposers.size() > 0">
                and wvd.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and wvd.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="ids != null and ids.size()>0">
                and wvd.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and wvd.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and wvd.web_url in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="title != null  and title != ''"> and wvd.title like concat('%',#{title},'%')</if>
            <if test="category != null  and category != ''">and wvd.category = #{category}</if>
            <if test="severity != null"> and wvd.severity = #{severity}</if>
            <if test="webUrl != null  and webUrl != ''"> and wvd.web_url like concat('%',#{webUrl},'%')</if>
            <if test="dataSource != null "> and wvd.data_source = #{dataSource}</if>
            <if test="scanNum != null "> and wvd.scan_num = #{scanNum}</if>
            <if test="flowState != null and flowState != '' and flowState != 99"> and wo.flow_state = #{flowState}</if>
            <if test="flowState == 99"> and wo.flow_state is null</if>
            <if test="handleState != null "> and wvd.handle_state = #{handleState}</if>
            <if test="createTime != null">and DATE_FORMAT(wvd.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="deptId != null">and (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
            <if test="applicationId != null">and tba.asset_id=#{applicationId}</if>
            <if test="startTime != null"> and wvd.create_time >= #{startTime} </if>
            <if test="endTime != null"> and wvd.create_time &lt;= #{endTime} </if>
            <if test="params.dataScope != null and params.dataScope != ''"> ${params.dataScope}</if>
        </where>
        group by wvd.id
        order by wvd.update_time desc
    </select>

    <select id="getMonitorBssWebVulnDealDetail" resultType="com.ruoyi.monitor2.domain.MonitorBssWebvulnDeal" resultMap="MonitorBssWebvulnDealResult">
        select wvd.*,tba.asset_name, vi.title as vi_title, vi.poc as vi_poc, vi.category as vi_category, vi.severity as vi_severity, vi.exposures as vi_exposures,
               vi.cvss as vi_cvss, vi.published_date_time as vi_published_date_time, vi.exp as vi_exp, vi.impact as vi_impact, vi.summary as vi_summary,
               vi.detail as vi_detail, vi.solution as vi_solution,fwvr.evidence
        from monitor_bss_webvuln_deal wvd left join
        tbl_business_application tba on wvd.web_url = tba.url left join
        monitor_bss_vuln_info vi on wvd.title = vi.title
        left join ffsafe_webscan_vulnresult fwvr on fwvr.vuln_name = wvd.title and fwvr.url = wvd.web_url
        <where>
            <if test="id != null  and id != ''"> and wvd.id = #{id}</if>
            <if test="title != null  and title != ''"> and wvd.title = #{title}</if>
            <if test="dataSource != null "> and wvd.data_source = #{dataSource}</if>
            <if test="webUrl != null "> and wvd.web_url = #{webUrl}</if>
        </where>
    </select>

    <select id="getMonitorBssWebvulnDealList" parameterType="MonitorBssWebvulnDeal" resultMap="MonitorBssWebvulnDealResult">
        SELECT wvd.*,tba.asset_name FROM monitor_bss_webvuln_deal wvd LEFT JOIN tbl_work_order wo ON FIND_IN_SET(wvd.id, wo.event_ids)
        left join tbl_business_application tba on wvd.web_url = tba.url
        <where>
            (work_type = '2' or work_type is null) and (flow_state != '4' or flow_state is null)
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="severity != null"> and severity = #{severity}</if>
            <if test="webUrl != null  and webUrl != ''"> and web_url = #{webUrl}</if>
        </where>
    </select>

    <select id="findMonitorBssWebvulnDealList" parameterType="MonitorBssWebvulnDeal" resultMap="MonitorBssWebvulnDealResult">
        select id, title, category, severity, web_url, asset_id, dept_id, scan_num, data_source FROM monitor_bss_webvuln_deal
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="webUrl != null  and webUrl != ''"> and web_url = #{webUrl}</if>
            and handle_state != 1
        </where>
    </select>

    <select id="selectMonitorBssWebvulnDealById" parameterType="Long" resultMap="MonitorBssWebvulnDealResult">
        <include refid="selectMonitorBssWebvulnDealVo"/>
        where id = #{id}
    </select>

    <select id="selectMonitorBssWebvulnDealByIds" parameterType="Long" resultMap="MonitorBssWebvulnDealResult">
        <include refid="selectMonitorBssWebvulnDealVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMonitorBssWebvulnDeal" parameterType="MonitorBssWebvulnDeal" useGeneratedKeys="true" keyProperty="id">
        insert into monitor_bss_webvuln_deal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="category != null">category,</if>
            <if test="severity != null">severity,</if>
            <if test="webUrl != null">web_url,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="scanNum != null">scan_num,</if>
            <if test="dataSource != null">data_source,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="disposer != null">disposer,</if>
            <if test="synchronizationStatus != null">synchronization_status,</if>
            <if test="dataId != null">data_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="category != null">#{category},</if>
            <if test="severity != null">#{severity},</if>
            <if test="webUrl != null">#{webUrl},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="scanNum != null">#{scanNum},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="disposer != null">#{disposer},</if>
            <if test="synchronizationStatus != null">#{synchronizationStatus},</if>
            <if test="dataId != null">#{dataId},</if>
        </trim>
    </insert>

    <update id="updateMonitorBssWebvulnDeal" parameterType="MonitorBssWebvulnDeal">
        update monitor_bss_webvuln_deal
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="category != null">category = #{category},</if>
            <if test="severity != null">severity = #{severity},</if>
            <if test="webUrl != null">web_url = #{webUrl},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="scanNum != null">scan_num = #{scanNum},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="disposer != null">disposer = #{disposer},</if>
            <if test="synchronizationStatus != null">synchronization_status = #{synchronizationStatus},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateBatchMonitorBssWebvulnDeal" parameterType="MonitorBssWebvulnDeal">
        update monitor_bss_webvuln_deal
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="category != null">category = #{category},</if>
            <if test="severity != null">severity = #{severity},</if>
            <if test="webUrl != null">web_url = #{webUrl},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="scanNum != null">scan_num = #{scanNum},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="disposer != null">disposer = #{disposer},</if>
            <if test="synchronizationStatus != null">synchronization_status = #{synchronizationStatus},</if>
        </trim>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="batchUpdateHandleState">
        UPDATE monitor_bss_webvuln_deal SET
        handle_state = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleState != null">#{item.handleState}</if> <if test="item.handleState == null">handle_state</if>
        </foreach>
        ELSE handle_state
        END,
        disposer = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.disposer != null">#{item.disposer}</if> <if test="item.disposer == null">disposer</if>
        </foreach>
        ELSE disposer
        END,
        handle_time = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleTime != null">#{item.handleTime}</if> <if test="item.handleTime == null">handle_time</if>
        </foreach>
        ELSE handle_time
        END,
        handle_desc = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleDesc != null">#{item.handleDesc}</if> <if test="item.handleDesc == null">handle_desc</if>
        </foreach>
        ELSE handle_desc
        END
        where id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <delete id="deleteMonitorBssWebvulnDealById" parameterType="Long">
        delete from monitor_bss_webvuln_deal where id = #{id}
    </delete>

    <delete id="deleteMonitorBssWebvulnDealByIds" parameterType="String">
        delete from monitor_bss_webvuln_deal where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getDealSeverity" resultType="java.util.HashMap">
        select
            CASE severity
              WHEN '0' THEN '未知'
              WHEN '1' THEN '低危'
              WHEN '2' THEN '中危'
              WHEN '3' THEN '高危'
              WHEN '4' THEN '严重'
            END as name,
            count(severity) as value
        from monitor_bss_webvuln_deal
        group by severity
    </select>

    <select id="getWebvulnCount" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        SUM(IF(severity = 1,1,0)) `none`,
        SUM(IF(severity = 2,1,0)) `low`,
        SUM(IF(severity = 3,1,0)) `medium`,
        SUM(IF(severity = 4,1,0)) `high`,
        web_url webUrl
        FROM
        monitor_bss_webvuln_deal
        WHERE
        web_url in
        <foreach item="url" collection="url" open="(" separator="," close=")">
            #{url}
        </foreach>
        GROUP BY
        web_url
    </select>

    <select id="getWebGapLinkBusinessList" parameterType="MonitorBssWebvulnDeal" resultMap="MonitorBssWebvulnDealResult">
        SELECT wvd.*,wo.flow_state,wo.id AS workId,wo.prod_id, wh.handle_state FROM monitor_bss_webvuln_deal wvd
        LEFT JOIN tbl_work_order wo ON FIND_IN_SET(wvd.id,wo.event_ids) AND wo.work_type = '2'
        left join tbl_work_history wh on wh.work_id = wo.id AND wh.id = (SELECT twh.id FROM tbl_work_history twh WHERE twh.work_id=wo.id ORDER BY twh.create_time DESC LIMIT 1)
        LEFT JOIN tbl_business_application ba ON wvd.web_url = ba.url
        <where>
            <if test="assetId != null  and assetId != ''">
                ba.asset_id = #{assetId}
            </if>
        </where>
        order by wvd.update_time desc
    </select>

    <select id="getRickLevelStat" resultType="java.util.HashMap">
        select d.severity, count(wvd.id) as num from (
            select 0 as severity union all
            select 1 union all
            select 2 union all
            select 3 union all
            select 4
        ) d left join monitor_bss_webvuln_deal wvd on d.severity = wvd.severity
        LEFT JOIN tbl_work_order wo ON FIND_IN_SET(wvd.id,wo.event_ids) AND wo.work_type = '2'
        left join (SELECT * FROM tbl_business_application GROUP BY url) tba on wvd.web_url = tba.url
        LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tba.domain_id
        left join sys_dept sd on sd.dept_id = tba.dept_id
        <where>
            <if test="startTime != null and endTime != null">
                and wvd.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="params.dataScope != null and params.dataScope != ''"> ${params.dataScope}</if>
        </where>
        group by d.severity
        order by d.severity desc
    </select>

    <select id="getHandleStateStat" resultType="java.util.HashMap">
        select d.handle_state, count(wvd.id) as num from (
            select 0 as handle_state union all
            select 1 union all
            select 2 union all
            select 3
        ) d left join monitor_bss_webvuln_deal wvd on d.handle_state = wvd.handle_state
        where wvd.severity != 0
        group by d.handle_state
        order by d.handle_state desc
    </select>

    <select id="getWebVulnTypeList" resultType="java.util.HashMap">
        select distinct category from monitor_bss_webvuln_deal
        group by category
    </select>
    <select id="selectNoSyncList" resultType="com.ruoyi.monitor2.domain.MonitorBssWebvulnDeal" resultMap="MonitorBssWebvulnDealBaseResult">
        select * from monitor_bss_webvuln_deal where (synchronization_status = '0' OR synchronization_status IS NULL)
        and severity > 1
    </select>

    <select id="countWebVulnDealNum" resultType="java.lang.Integer">
        SELECT
        COUNT( t1.id )
        FROM
        monitor_bss_webvuln_deal t1
        <if test="deptId != 100">
            RIGHT JOIN(
            SELECT
            wvd.*,
            wo.flow_state,
            wo.id AS workId,
            wo.prod_id,
            GROUP_CONCAT( tba.asset_name ) asset_name,
            GROUP_CONCAT( sd.dept_name ) dept_name
            FROM
            monitor_bss_webvuln_deal wvd
            LEFT JOIN tbl_work_order wo ON FIND_IN_SET( wvd.id, wo.event_ids )
            AND wo.work_type = '2'
            LEFT JOIN tbl_business_application tba ON wvd.web_url = tba.url
            LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tba.domain_id
            LEFT JOIN sys_dept sd ON sd.dept_id = tba.dept_id
            WHERE
            (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
            group by wvd.id
            order by wvd.update_time desc
            )t2 ON t1.id = t2.id
        </if>
        <where>
            <if test="startTime != null and endTime != null">
                and t1.update_time between #{startTime} and #{endTime}
            </if>
        </where>
    </select>

    <select id="getStatisticsOnWebVulnerabilityTypes" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        CASE
        WHEN d.severity =1 THEN '低危'
        WHEN d.severity =2 THEN '中危'
        WHEN d.severity =3 THEN '高危'
        WHEN d.severity =4 THEN '严重'
        ELSE '未知'
        END AS severity,  count(DISTINCT wvd.id) as num from (
            select 1 as severity union all
            select 2 union all
            select 3 union all
            select 4
        ) d LEFT JOIN monitor_bss_webvuln_deal wvd ON d.severity = wvd.severity
            LEFT JOIN tbl_work_order wo ON FIND_IN_SET( wvd.id, wo.event_ids )
            AND wo.work_type = '2'
            LEFT JOIN tbl_business_application tba ON wvd.web_url = tba.url
            LEFT JOIN tbl_network_domain tnd ON tnd.domain_id = tba.domain_id
            LEFT JOIN sys_dept sd ON sd.dept_id = tba.dept_id
        <where>
            wvd.severity not in (0)
            <if test="startTime != null and endTime != null">
                and wvd.update_time between #{startTime} and #{endTime}
            </if>
            <if test="deptId != null">and (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))</if>
        </where>
        group by d.severity
    </select>

    <select id="getFindTheFirstMonitorBssWebvulnDealId" resultType="java.lang.String">
        SELECT
            id
        FROM
            monitor_bss_webvuln_deal
        ORDER BY
            id DESC
            LIMIT 1
    </select>
</mapper>

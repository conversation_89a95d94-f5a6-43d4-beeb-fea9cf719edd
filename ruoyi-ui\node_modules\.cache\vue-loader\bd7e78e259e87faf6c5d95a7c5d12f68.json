{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\boundary\\BoundaryDetailDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\boundary\\BoundaryDetailDialog.vue", "mtime": 1754969114219}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BoundaryDetailDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BoundaryDetailDialog.vue", "sourceRoot": "src/views/safe/boundary", "sourcesContent": ["<template>\n  <el-dialog :visible.sync=\"visible\" :title=\"title\" width=\"50%\">\n    <div class=\"customForm-container\" style=\"padding: 0 20px\">\n      <div class=\"my-title\"><img src=\"@/assets/images/application/jbxx.png\" alt=\"\">基本信息</div>\n      <el-descriptions\n        class=\"custom-column\"\n        direction=\"vertical\"\n        size=\"medium\"\n        :colon=\"false\"\n        label-class-name=\"custom-label-style\"\n        content-class-name=\"custom-content-style\"\n        :column=\"2\"\n      >\n        <template v-for=\"item in visibleAssetFields\">\n          <el-descriptions-item :key=\"item.fieldKey\" :label=\"item.fieldName\">\n            <!-- 字典字段特殊处理 -->\n            <template v-if=\"dictFields.includes(item.fieldKey)\">\n              <span\n                v-for=\"dict in getDictData(item.fieldKey)\"\n                :key=\"dict.value\"\n                v-if=\"dict.value === form[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <template v-if=\"item.fieldKey === 'deptId'\">\n              <span\n                v-for=\"dict in deptOptions\"\n                :key=\"dict.id\"\n                v-if=\"dict.id === form[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <!-- 普通字段直接显示 -->\n            <template v-else>\n              {{ getFieldValue(item.fieldKey) }}\n            </template>\n          </el-descriptions-item>\n        </template>\n      </el-descriptions>\n\n      <div class=\"my-title\" style=\"margin-top: 20px;\">\n        <i class=\"el-icon-office-building icon\" />\n        区域内资产\n      </div>\n      <asset-over-view-table\n        style=\"width: 100%;\n              padding-right: 10px;\"\n        :disabled=\"!editable\"\n        :if-reflush=\"dialogVisible\"\n        :asset-overview-query=\"queryParamsBoundaryAsset\"\n        :asset-class=\"[2, 3, 5, 9]\"\n        :asset-find-fun=\"getBoundaryAssetsList\"\n        :asset-add-fun=\"handleAddAsset\"\n        :asset-delete-fun=\"handleDeleteAsset\"\n      />\n\n      <div class=\"my-title\" style=\"margin-top: 20px;\">\n        <i class=\"el-icon-files icon\" />\n        相关文件\n      </div>\n      <asset-file style=\"width: 100%; padding-right: 10px;\"\n                  :disabled=\"!editable\"\n                  :asset-id=\"form.assetId\"\n      />\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport assetOverViewTable from \"@/views/components/table/assetOverViewTable.vue\";\nimport AssetFile from \"@/views/dimension/file/index.vue\";\nimport {deptTreeSelect, getAllDeptTree} from \"@/api/system/user\";\n\nexport default {\n  name: \"BoundaryDetailDialog\",\n  components: {AssetFile, assetOverViewTable},\n  props: {\n    boundaryDetailVisible: {\n      type: Boolean,\n      default: false\n    },\n    title: {\n      type: String,\n      default: '查看边界区域'\n    },\n    form: Object,\n    editable: Boolean,\n    editItem: String,\n    children: Array,\n    queryParamsBoundaryAsset: Object,\n    dictOptions: Array,\n    getBoundaryAssetsList: Function,\n    handleAddAsset: Function,\n    handleDeleteAsset: Function,\n    assetList: {\n      type: Array,\n      default: function () {\n        return []\n      }\n    }\n  },\n  data() {\n    return {\n      dialogVisible: this.visible,\n      deptOptions: []\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.boundaryDetailVisible;\n      },\n      set(val) {\n        this.$emit('update:boundaryDetailVisible', val);\n      }\n    },\n\n    visibleAssetFields() {\n      return this.assetList.filter(item => item.isShow);\n    },\n\n    dictFields() {\n      return ['degreeImportance'];\n    }\n  },\n  created() {\n    this.getDeptTree();\n  },\n  methods: {\n    getDictData(fieldKey) {\n      switch (fieldKey) {\n        case 'degreeImportance':\n          return this.dictOptions;\n        default:\n          return [];\n      }\n    },\n    /** 查询所属部门 */\n    getDeptTree() {\n      getAllDeptTree().then(response => {\n        this.deptOptions = response.data;\n      });\n    },\n    getFieldValue(field) {\n      if(this.dictFields.includes(field)){\n        return '';\n      }\n      if (field === 'assetType') {\n        return this.form.assetTypeDesc || this.form[field];\n      }\n      return this.form[field];\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n::v-deep .el-dialog__body {\n  padding: 0 0 20px 0 !important;\n}\n</style>\n"]}]}
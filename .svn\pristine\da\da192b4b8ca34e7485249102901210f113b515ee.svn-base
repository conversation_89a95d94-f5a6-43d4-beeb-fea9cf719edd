<template>
  <el-dialog :visible.sync="visible" :title="title" width="50%">
    <div class="customForm-container" style="padding: 0 20px">
      <div class="my-title"><img src="@/assets/images/application/jbxx.png" alt="">基本信息</div>
      <el-descriptions
        class="custom-column"
        direction="vertical"
        size="medium"
        :colon="false"
        label-class-name="custom-label-style"
        content-class-name="custom-content-style"
        :column="2"
      >
        <template v-for="item in visibleAssetFields">
          <el-descriptions-item :key="item.fieldKey" :label="item.fieldName">
            <!-- 字典字段特殊处理 -->
            <template v-if="dictFields.includes(item.fieldKey)">
              <span
                v-for="dict in getDictData(item.fieldKey)"
                :key="dict.value"
                v-if="dict.value === form[item.fieldKey]"
              >
                {{ dict.label }}
              </span>
            </template>

            <template v-if="item.fieldKey === 'deptId'">
              <span
                v-for="dict in deptOptions"
                :key="dict.id"
                v-if="dict.id === form[item.fieldKey]"
              >
                {{ dict.label }}
              </span>
            </template>

            <!-- 普通字段直接显示 -->
            <template v-else>
              {{ getFieldValue(item.fieldKey) }}
            </template>
          </el-descriptions-item>
        </template>
      </el-descriptions>

      <div class="my-title" style="margin-top: 20px;">
        <i class="el-icon-office-building icon" />
        区域内资产
      </div>
      <asset-over-view-table
        style="width: 100%;
              padding-right: 10px;"
        :disabled="!editable"
        :if-reflush="dialogVisible"
        :asset-overview-query="queryParamsBoundaryAsset"
        :asset-class="[2, 3, 5, 9]"
        :asset-find-fun="getBoundaryAssetsList"
        :asset-add-fun="handleAddAsset"
        :asset-delete-fun="handleDeleteAsset"
      />

      <div class="my-title" style="margin-top: 20px;">
        <i class="el-icon-files icon" />
        相关文件
      </div>
      <asset-file style="width: 100%; padding-right: 10px;"
                  :disabled="!editable"
                  :asset-id="form.assetId"
      />
    </div>
  </el-dialog>
</template>

<script>
import assetOverViewTable from "@/views/components/table/assetOverViewTable.vue";
import AssetFile from "@/views/dimension/file/index.vue";
import {deptTreeSelect, getAllDeptTree} from "@/api/system/user";

export default {
  name: "BoundaryDetailDialog",
  components: {AssetFile, assetOverViewTable},
  props: {
    boundaryDetailVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '查看边界区域'
    },
    form: Object,
    editable: Boolean,
    editItem: String,
    children: Array,
    queryParamsBoundaryAsset: Object,
    dictOptions: Array,
    getBoundaryAssetsList: Function,
    handleAddAsset: Function,
    handleDeleteAsset: Function,
    assetList: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      deptOptions: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.boundaryDetailVisible;
      },
      set(val) {
        this.$emit('update:boundaryDetailVisible', val);
      }
    },

    visibleAssetFields() {
      return this.assetList.filter(item => item.isShow);
    },

    dictFields() {
      return ['degreeImportance'];
    }
  },
  created() {
    this.getDeptTree();
  },
  methods: {
    getDictData(fieldKey) {
      switch (fieldKey) {
        case 'degreeImportance':
          return this.dictOptions;
        default:
          return [];
      }
    },
    /** 查询所属部门 */
    getDeptTree() {
      getAllDeptTree().then(response => {
        this.deptOptions = response.data;
      });
    },
    getFieldValue(field) {
      if(this.dictFields.includes(field)){
        return '';
      }
      if (field === 'assetType') {
        return this.form.assetTypeDesc || this.form[field];
      }
      return this.form[field];
    },
  },
}
</script>

<style scoped lang="scss">
@import "@/assets/styles/customForm";
::v-deep .el-dialog__body {
  padding: 0 0 20px 0 !important;
}
</style>

# TblServer导入DTO优化项目计划

## 项目概述

**目标**: 优化TblServerController.importJtTemplate接口，创建专用的导入DTO类，提高代码质量和用户体验。

**问题**: 当前接口直接使用TblServer实体类（50+字段），导致Excel模板复杂，用户体验差。

**解决方案**: 创建轻量级TblServerImportDTO类，只包含14个核心导入字段。

## 核心字段分析

### 必填字段（11个）
1. 资产名称 (assetName)
2. 设备厂商 (facilityManufacturerName) 
3. 设备型号 (facilityType)
4. 业务应用 (applicationName)
5. IP地址 (ip)
6. 操作系统 (systemName)
7. 数据库 (dbSystemName)
8. 是否热备 (isSparing)
9. 物理位置 (locationName)
10. 管理部门 (deptName)
11. 供应商 (vendorName)

### 选填字段（3个）
1. MAC地址 (mac)
2. 主机名 (hostName)
3. 备注 (remark)

## 任务分解

### 任务1: 创建TblServerImportDTO类
- **ID**: 46326282-fd3d-4aa0-85ec-c2945cadfbaf
- **文件**: `aqsoc-monitor/src/main/java/com/ruoyi/safe/dto/TblServerImportDTO.java`
- **关键点**: 
  - 14个核心字段
  - @Excel注解配置（sort排序）
  - @NotBlank验证注解
  - 完整JavaDoc注释

### 任务2: 修改importJtTemplate接口
- **ID**: 42521382-5ef5-46ff-a923-aef3944c9891
- **文件**: `aqsoc-monitor/src/main/java/com/ruoyi/safe/controller/TblServerController.java`
- **修改范围**: 第345-419行
- **关键点**:
  - ExcelUtil泛型改为TblServerImportDTO
  - 调整示例数据设置
  - 保持注意事项说明

### 任务3: 测试Excel模板生成
- **ID**: 1f990278-09cf-4ddc-8d31-8c1c688ae8e5
- **验证点**:
  - 字段数量从50+减少到14个
  - 必填字段有*标识
  - 示例数据完整有意义

## 字段排序设计

1. **基本信息**(1-3): 资产名称、设备厂商、设备型号
2. **网络信息**(4-6): IP地址、MAC地址、主机名
3. **系统信息**(7-8): 操作系统、数据库
4. **业务信息**(9-10): 业务应用、是否热备
5. **位置信息**(11-12): 物理位置、管理部门
6. **其他信息**(13-14): 供应商、备注

## 预期效果

- ✅ 简化Excel模板，提升用户体验
- ✅ 代码结构更清晰，职责单一
- ✅ 降低维护成本
- ✅ 为将来导入功能扩展奠定基础

## 风险评估

- **低风险**: 只修改模板下载，不影响现有业务
- **向后兼容**: 保持原importTemplate方法不变
- **测试重点**: Excel模板生成和字段显示

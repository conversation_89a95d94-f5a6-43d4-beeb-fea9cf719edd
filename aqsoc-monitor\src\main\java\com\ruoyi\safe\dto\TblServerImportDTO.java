package com.ruoyi.safe.dto;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 服务器导入DTO
 * 专用于Excel导入功能的轻量级数据传输对象
 * 只包含导入相关的核心字段，提高用户体验和代码质量
 *
 * <AUTHOR> 4.0 sonnet
 * @date 2025-01-11
 */
@Data
@ApiModel(value = "TblServerImportDTO", description = "服务器导入数据传输对象")
public class TblServerImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 资产名称（必填）
     */
    @NotBlank(message = "资产名称不能为空")
    @Excel(name = "*资产名称", sort = 1)
    @ApiModelProperty(value = "资产名称", required = true, example = "资产数据服务器07")
    private String assetName;

    /**
     * 设备厂商（必填）
     */
    @NotBlank(message = "设备厂商不能为空")
    @Excel(name = "*设备厂商", sort = 2)
    @ApiModelProperty(value = "设备厂商", required = true, example = "某某技术有限公司")
    private String facilityManufacturerName;

    /**
     * 设备型号（必填）
     */
    @NotBlank(message = "设备型号不能为空")
    @Excel(name = "*设备型号", sort = 3)
    @ApiModelProperty(value = "设备型号", required = true, example = "WY IDJ008")
    private String facilityType;

    /**
     * IP地址（必填）
     */
    @NotBlank(message = "IP地址不能为空")
    @Pattern(regexp = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$", 
             message = "IP地址格式不正确")
    @Excel(name = "*IP地址", sort = 4)
    @ApiModelProperty(value = "IP地址", required = true, example = "*************")
    private String ip;

    /**
     * MAC地址（选填）
     */
    @Excel(name = "MAC地址", sort = 5)
    @ApiModelProperty(value = "MAC地址", example = "A0:01:0E:4W:9O:1X")
    private String mac;

    /**
     * 主机名称（选填）
     */
    @Excel(name = "主机名称", sort = 6)
    @ApiModelProperty(value = "主机名称", example = "某某主机")
    private String hostName;

    /**
     * 操作系统（必填）
     */
    @NotBlank(message = "操作系统不能为空")
    @Excel(name = "*操作系统", sort = 7)
    @ApiModelProperty(value = "操作系统", required = true, example = "CentOS 6 64bit")
    private String systemName;

    /**
     * 数据库管理系统（必填）
     */
    @NotBlank(message = "数据库管理系统不能为空")
    @Excel(name = "*数据库管理系统", sort = 8)
    @ApiModelProperty(value = "数据库管理系统", required = true, example = "MySQL")
    private String dbSystemName;

    /**
     * 业务应用（必填）
     */
    @NotBlank(message = "业务应用不能为空")
    @Excel(name = "*业务应用", sort = 9)
    @ApiModelProperty(value = "业务应用", required = true, example = "某某业务系统")
    private String applicationName;

    /**
     * 是否热备（必填）
     */
    @NotBlank(message = "是否热备不能为空")
    @Excel(name = "*是否热备", sort = 10, dictType = "is_sparing")
    @ApiModelProperty(value = "是否热备", required = true, example = "是")
    private String isSparing;

    /**
     * 物理位置（必填）
     */
    @NotBlank(message = "物理位置不能为空")
    @Excel(name = "*物理位置", sort = 11)
    @ApiModelProperty(value = "物理位置", required = true, example = "南昌市红谷滩办公大楼3楼机房A1机柜1U")
    private String locationName;

    /**
     * 管理部门（必填）
     */
    @NotBlank(message = "管理部门不能为空")
    @Excel(name = "*管理部门", sort = 12)
    @ApiModelProperty(value = "管理部门", required = true, example = "某某部门")
    private String deptName;

    /**
     * 供应商（必填）
     */
    @NotBlank(message = "供应商不能为空")
    @Excel(name = "*供应商", sort = 13)
    @ApiModelProperty(value = "供应商", required = true, example = "某某技术有限公司")
    private String vendorName;

    /**
     * 备注（选填）
     */
    @Excel(name = "备注", sort = 14)
    @ApiModelProperty(value = "备注", example = "需要定期维护与数据扫码")
    private String remark;

    /**
     * 默认构造函数
     */
    public TblServerImportDTO() {
        // 设置默认值
        this.isSparing = "否";
    }

    /**
     * 带参数的构造函数
     */
    public TblServerImportDTO(String assetName, String facilityManufacturerName, String facilityType, 
                             String ip, String systemName, String dbSystemName, String applicationName, 
                             String locationName, String deptName, String vendorName) {
        this();
        this.assetName = assetName;
        this.facilityManufacturerName = facilityManufacturerName;
        this.facilityType = facilityType;
        this.ip = ip;
        this.systemName = systemName;
        this.dbSystemName = dbSystemName;
        this.applicationName = applicationName;
        this.locationName = locationName;
        this.deptName = deptName;
        this.vendorName = vendorName;
    }
}

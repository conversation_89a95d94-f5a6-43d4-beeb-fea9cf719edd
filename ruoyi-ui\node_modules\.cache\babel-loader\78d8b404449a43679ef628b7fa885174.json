{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue", "mtime": 1754969114059}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_application", "require", "_applicationLink", "_interopRequireDefault", "_applicationSite", "_userSelect", "_deptSelect", "_networkSelect", "_DynamicTag", "_vendorSelect", "_dictSelect", "_utils", "_ruoyi", "_vendor", "_serverEV", "_dateEV", "_networkEV", "_safeEV", "_overViewSelect", "_overview", "_editServer", "_user", "_domain", "name", "components", "EditServer", "overViewSelect", "safeEV", "networkEV", "dateEV", "serverEV", "ApplicationLink", "ApplicationSite", "UserSelect", "DeptSelect", "NetworkSelect", "DictSelect", "DynamicTag", "VendorSelect2", "dicts", "inject", "$editable", "default", "value", "props", "assetId", "type", "String", "Number", "required", "changeId", "Function", "readonly", "Boolean", "disabled", "assetList", "Array", "data", "loading", "collapseNames", "vendorsdata", "userdata", "functionStateList", "form", "businessForm", "delList", "deptOptions", "gv", "getValFromObject", "deployLocation", "localStorage", "getItem", "<PERSON><PERSON><PERSON><PERSON>", "managePlaceholder", "<PERSON><PERSON><PERSON>", "networkDomainOptions", "vendorsData", "refs", "collapse", "showAddServer", "serverOptions", "currentAssociationServer", "afterInit", "uploadType", "selectType", "mounted", "_this", "getAllServerList", "getDeptTree", "getManagerList", "getNetworkDomainTree", "getVendorsData", "$nextTick", "reset", "init", "computed", "getServerName", "_this2", "id", "_this2$serverOptions$", "find", "s", "assetName", "getServerIp", "_this3", "_this3$serverOptions$", "ip", "processedManagerList", "_this4", "ids", "_toConsumableArray2", "Set", "manager", "split", "filter", "map", "user", "u", "userId", "nick<PERSON><PERSON>", "phone", "phonenumber", "processedVendorsList", "_this5", "vendors", "vendorName", "processedServiceGroups", "_this6", "serviceGroup", "val", "_this6$dict$type$serv", "dict", "d", "label", "activated", "_this7", "watch", "handler", "newVal", "oldVal", "length", "for<PERSON>ach", "item", "index", "Object", "keys", "tempId", "generateSecureUUID", "systemType", "toString", "methods", "getFieldValue", "field", "_this8", "filterArr", "includes", "<PERSON><PERSON><PERSON>", "_this$dict$type$hw_is", "hw_is_true_shut_down", "getFieldSpan", "fullSpanFields", "shouldShowField", "isadapt", "iscipher", "getDictOptions", "dictMap", "construct", "loginType", "technical", "deploy", "state", "protectGrade", "evaluationResults", "evaluationStatus", "hwIsTrueShutDown", "_this9", "listAllOverview", "then", "res", "_this10", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getApplication", "response", "applicationVO", "waitForValue", "$refs", "site", "getList", "tblBusinessApplication", "userNums", "everydayVisitNums", "everydayActiveNums", "tblMapperList", "i", "push", "finally", "stop", "undefined", "assetCode", "softwareVersion", "degreeImportance", "domainUrl", "assetType", "assetTypeDesc", "assetClass", "assetClassDesc", "netType", "appType", "frequency", "usageCount", "userScale", "userObject", "url", "ipd", "storage", "netenv", "iskey", "datanum", "isbase", "islink", "ishare", "islog", "isplan", "adaptDate", "cipherDate", "function", "remark", "deptId", "orgnId", "upTime", "dwid", "contactor", "domainId", "netScale", "netTopo", "netMemo", "tags", "links", "eids", "sysBusinessState", "resetForm", "serverSelect", "$set", "serverId", "_this11", "getAllDeptTree", "deptTreeSelect", "_this12", "listUser", "isAsc", "orderByColumn", "isAllData", "userName", "rows", "_this13", "listDomain", "_this14", "listVendorByApplication", "applicationId", "applicationCode", "pageNum", "pageSize", "vendorCode", "vendorManageName"], "sources": ["src/views/hhlCode/component/OperationSystemDetails.vue"], "sourcesContent": ["<!--业务系统详情-->\n<template>\n  <div class=\"customForm-container\" style=\"height: 65vh\">\n    <template v-for=\"group in assetList\">\n      <div :key=\"group.formName\" style=\"margin-bottom: 20px;\">\n        <div class=\"my-title\">\n          <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '备案信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '测评信息'\" src=\"@/assets/images/application/cpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '外部连接信息'\" src=\"@/assets/images/application/wblj.png\" alt=\"\">\n          <img v-if=\"group.formName === '拓扑结构信息'\" src=\"@/assets/images/application/tpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '运营维护情况'\" src=\"@/assets/images/application/ywxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '其他基本信息'\" src=\"@/assets/images/application/qtxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '用户规模'\" src=\"@/assets/images/application/yhgm.png\" alt=\"\">\n          <img v-if=\"group.formName === '业务描述'\" src=\"@/assets/images/application/ywms.png\" alt=\"\">\n          <img v-if=\"group.formName === '功能模块'\" src=\"@/assets/images/application/gnmk.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装服务器环境'\" src=\"@/assets/images/application/fwq.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装数据库环境'\" src=\"@/assets/images/application/sjk.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联网络设备'\" src=\"@/assets/images/application/wlsb.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联安全设备'\" src=\"@/assets/images/application/aqsb.png\" alt=\"\">\n          {{ group.formName }}\n        </div>\n        <template v-if=\"group.formName === '外部连接信息'\">\n          <ApplicationLink\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            v-model=\"form.links\"/>\n        </template>\n        <template v-else-if=\"group.formName === '运营维护情况'\">\n          <ApplicationSite\n            ref=\"site\"\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            :value.sync=\"form.eids\"\n            :asset-id=\"form.assetId\"\n            multiple/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装服务器环境'\">\n          <serverEV\n            class=\"my-form\"\n            ref=\"serverEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"\n            :data-list=\"currentAssociationServer\"\n            @selected=\"serverSelect\"\n            v-if=\"afterInit\"/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装数据库环境'\">\n          <dateEV\n            class=\"my-form\"\n            ref=\"dateEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联网络设备'\">\n          <network-e-v\n            class=\"my-form\"\n            ref=\"networkEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联安全设备'\">\n          <safeEV\n            class=\"my-form\"\n            ref=\"safeEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '功能模块'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item :span=\"3\" v-for=\"(item, index) in functionStateList\" :key=\"index\">\n              <div style=\"display: flex; justify-content: space-around\">\n                <div>{{ item.moduleName }}</div>\n                <div>{{ item.moduleDesc }}</div>\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '用户规模'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === businessForm[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n              <template v-else-if=\"field.fieldKey === 'serviceGroup'\">\n                <div class=\"tag-group\">\n                  <template v-if=\"processedServiceGroups.length > 0\">\n                    <span v-for=\"(label, index) in processedServiceGroups\" :key=\"index\">\n                      {{ label }}\n                    </span>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择</span>\n                </div>\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '业务描述'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"businessForm[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else>\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              v-if=\"shouldShowField(field)\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n\n              <!-- 上传类型字段 -->\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"form[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n\n              <!-- 特殊字段：关联服务器 -->\n              <template v-else-if=\"field.fieldKey === 'associationServer'\">\n                <div class=\"server-display\">\n                  <div v-for=\"id in form.associationServer\" :key=\"id\" class=\"server-item\">\n                    <span>{{ getServerName(id) }}</span>\n                  </div>\n                </div>\n              </template>\n\n              <!-- 特殊字段：责任人 -->\n              <template v-else-if=\"field.fieldKey === 'manager'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedManagerList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedManagerList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}（{{ user.phone }}）\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择责任人</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：单位 -->\n              <template v-else-if=\"field.fieldKey === 'deptId'\">\n                <span\n                  v-for=\"(item, index) in deptOptions\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.deptId\"\n                >{{ item.label }}</span>\n              </template>\n\n              <!-- 特殊字段：主部署网络 -->\n              <template v-else-if=\"field.fieldKey === 'domainId'\">\n                <span\n                  v-for=\"(item, index) in networkDomainOptions\"\n                  :key=\"item.domainId\"\n                  v-if=\"item.domainId === form.domainId\"\n                >{{ item.domainName }}</span>\n              </template>\n\n              <!-- 特殊字段：开发合作企业 -->\n              <template v-else-if=\"field.fieldKey === 'vendor'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedVendorsList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedVendorsList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择开发合作企业</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：标签 -->\n              <template v-else-if=\"field.fieldKey === 'tags'\">\n                <template v-if=\"(form.tags || '').split(',').filter(t => t).length > 0\">\n                  <el-tag\n                    v-for=\"(tag,index) in (form.tags || '').split(',')\"\n                    :key=\"index\"\n                    closable\n                    size=\"small\"\n                    v-show=\"tag\"\n                  >\n                    {{ tag }}\n                  </el-tag>\n                </template>\n                <span v-else class=\"gray-text\">暂无标签</span>\n              </template>\n\n              <!-- 下拉选择类型字段 -->\n              <template v-else-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === form[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n\n              <!-- 默认文本显示 -->\n              <template v-else>\n                <span>{{ getFieldValue(field) }}</span>\n              </template>\n            </el-descriptions-item>\n\n          </el-descriptions>\n        </template>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport {getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {getValFromObject} from \"@/utils\";\nimport {generateSecureUUID, waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\nimport serverEV from \"@/views/hhlCode/component/application/applicationHardware/serverEV.vue\";\nimport dateEV from \"@/views/hhlCode/component/application/applicationHardware/dateEV.vue\";\nimport networkEV from \"@/views/hhlCode/component/application/applicationHardware/networkEV.vue\";\nimport safeEV from \"@/views/hhlCode/component/application/applicationHardware/safeEV.vue\";\nimport overViewSelect from \"@/views/components/select/overViewSelect.vue\";\nimport {listAllOverview} from \"@/api/safe/overview\";\nimport EditServer from \"@/views/safe/server/editServer.vue\";\nimport {getAllDeptTree, deptTreeSelect, listUser} from \"@/api/system/user\"\nimport {listDomain} from \"@/api/dict/domain\";\n\nexport default {\n  name: \"OperationSystemDetails\",\n  components: {\n    EditServer,\n    overViewSelect,\n    safeEV,\n    networkEV,\n    dateEV,\n    serverEV,\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'serve_group',\n    'cover_area',\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status',\n    'is_open_network',\n    'hw_is_true_shut_down'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    assetList: {\n      type: Array,\n      default: () => []\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      functionStateList: [{}, {}, {}],\n      // 基本信息表单参数\n      form: {},\n      // 业务信息表单参数\n      businessForm: {\n        delList: []\n      },\n      deptOptions: [],\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人',\n      managerData: [],\n      networkDomainOptions: [],\n      vendorsData: [],\n      refs: {\n        'networkEV': \"所安装服务器环境\",\n        'safeEV': '所安装数据环境',\n        'serverEV': '关联网络设备',\n        'dateEV': \"关联安全设备\"\n      },\n      collapse: ['1', '2', '3', '4'],\n      showAddServer: false,\n      serverOptions: [],\n      currentAssociationServer: [],\n      afterInit: false,\n      uploadType: ['waitingInsuranceFilingScan', 'evaluationReport', 'netTopo', 'operateHandbook'],\n      selectType: ['systemType', 'construct', 'loginType', 'technical', 'deploy', 'state', 'protectGrade', 'evaluationResults', 'evaluationStatus'],\n    }\n  },\n  mounted() {\n    this.getAllServerList();\n    this.getDeptTree();\n    this.getManagerList();\n    this.getNetworkDomainTree();\n    this.getVendorsData();\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  computed: {\n    // 获取服务器名称映射\n    getServerName() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.assetName || ''\n    },\n    // 获取服务器IP映射\n    getServerIp() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.ip || ''\n    },\n    processedManagerList() {\n      // 去重\n      const ids = [...new Set(\n        (this.form.manager || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.managerData.find(u =>\n          Number(u.userId) === Number(id)\n        );\n        return {\n          id,\n          name: user?.nickName || '未知用户',\n          phone: user?.phonenumber || ''\n        };\n      });\n    },\n    processedVendorsList() {\n      const ids = [...new Set(\n        (this.form.vendors || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.vendorsData.find(u =>\n          Number(u.id) === Number(id)\n        );\n        return {\n          id,\n          name: user?.vendorName || '未知用户',\n        };\n      });\n    },\n    processedServiceGroups() {\n      if (!this.businessForm.serviceGroup) return []\n      return this.businessForm.serviceGroup.split(',')\n        .map(val => this.dict.type['serve_group'].find(d => d.value === val)?.label || val)\n    }\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n  watch: {\n    functionStateList: {\n      handler(newVal, oldVal) {\n        if (newVal && newVal.length > 0) {\n          newVal.forEach((item, index) => {\n            if (Object.keys(item).length > 0) {\n              item.tempId = generateSecureUUID();\n            }\n          })\n        }\n      },\n    },\n    'form.systemType': {\n      handler(newVal, oldVal) {\n        if (newVal) {\n          this.form.systemType = newVal.toString();\n        }\n      }\n    },\n  },\n  methods: {\n    getFieldValue(field) {\n      // 其他基本信息字段格式化\n      let filterArr = ['isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'iskey', 'isOpenNetwork']\n      if (filterArr.includes(field.fieldKey)) {\n        return this.form[field.fieldKey] === 'Y' ? '是' : '否';\n      }\n      if(field.fieldKey === 'hwIsTrueShutDown'){\n        return this.dict.type.hw_is_true_shut_down.find(d => d.value === this.form[field.fieldKey])?.label || this.form[field.fieldKey];\n      }\n      return this.form[field.fieldKey];\n    },\n\n    getFieldSpan(field) {\n      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan'];\n      if (fullSpanFields.includes(field.fieldKey)) return 3;\n      // 其他字段默认占8列\n      return 1;\n    },\n    // 判断字段是否显示\n    shouldShowField(field) {\n      if (field.fieldKey === 'otherSystemNotes') {\n        return this.form.systemType === '12';\n      }\n      if (field.fieldKey === 'adaptDate') {\n        return this.form.isadapt === 'Y';\n      }\n      if (field.fieldKey === 'cipherDate') {\n        return this.form.iscipher === 'Y';\n      }\n      if (field.fieldKey === 'islink') {\n        return this.deployLocation === 'fair';\n      }\n      return true;\n    },\n\n    getDictOptions(fieldKey) {\n      const dictMap = {\n        systemType: 'system_type',\n        construct: 'construct_type',\n        loginType: 'app_login_type',\n        technical: 'app_technical',\n        deploy: 'app_deploy',\n        state: 'asset_state',\n        protectGrade: 'protection_grade',\n        evaluationResults: 'evaluation_results',\n        evaluationStatus: 'evaluation_status',\n        hwIsTrueShutDown: 'hw_is_true_shut_down'\n      };\n      return this.dict.type[dictMap[fieldKey]] || [];\n    },\n\n    getAllServerList() {\n      listAllOverview({\"assetClass\": 4}).then(res => {\n        this.serverOptions = res.data;\n      })\n    },\n    /** 初始化 */\n    async init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        await getApplication(this.assetId).then(response => {\n          // 获取应用信息详情\n          this.form.assetId = this.assetId;\n          this.form = response.data.applicationVO;\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n          // 获取业务信息详情\n          this.businessForm.assetId = this.assetId;\n          this.businessForm = response.data.tblBusinessApplication;\n          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';\n          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';\n          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';\n          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];\n          if (this.functionStateList.length < 3) {\n            let i = 0;\n            while (i < 3 - this.functionStateList.length) {\n              this.functionStateList.push({});\n            }\n          }\n        }).finally(() => {\n          this.afterInit = true;\n        })\n      } else {\n        this.afterInit = true;\n      }\n    },\n\n\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.businessForm = {\n        sysBusinessState: undefined,\n        userNums: undefined,\n        everydayVisitNums: undefined,\n        everydayActiveNums: undefined,\n      };\n      this.resetForm(\"form\");\n      this.resetForm(\"businessForm\");\n    },\n\n    serverSelect(data) {\n      if (data) {\n        this.$set(this.form, 'associationServer', data.map(item => item.serverId))\n      }\n    },\n\n    /** 查询所属部门 */\n    getDeptTree() {\n      if (this.$editable.value) {\n        getAllDeptTree().then(response => {\n          this.deptOptions = response.data;\n        });\n      } else {\n        deptTreeSelect().then(response => {\n          this.deptOptions = response.data;\n        });\n      }\n    },\n\n    //  查询所有责任人/电话\n    getManagerList() {\n      listUser({\n        isAsc: 'desc',\n        orderByColumn: 'createTime',\n        isAllData: true,\n        userName: null,\n        nickName: null,\n        phonenumber: null,\n      }).then(response => {\n        this.managerData = response.rows;\n      });\n    },\n\n    /** 获取主部署网络 */\n    getNetworkDomainTree() {\n      listDomain().then(response => {\n        this.networkDomainOptions = response.data\n      });\n    },\n\n    /* 获取开发合作企业 */\n    getVendorsData() {\n      listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        isAsc: 'desc',\n        orderByColumn: null,\n        pageNum: 1,\n        pageSize: 10,\n        vendorCode: null,\n        vendorName: null,\n        vendorManageName: null,\n      }).then(response => {\n        this.vendorsData = response.rows;\n      });\n    }\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n\n.server-display {\n  line-height: 1.8;\n  display: flex;\n}\n\n.server-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 0 5px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAsRA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,WAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,cAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,WAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,aAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,WAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,SAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,OAAA,GAAAZ,sBAAA,CAAAF,OAAA;AACA,IAAAe,UAAA,GAAAb,sBAAA,CAAAF,OAAA;AACA,IAAAgB,OAAA,GAAAd,sBAAA,CAAAF,OAAA;AACA,IAAAiB,eAAA,GAAAf,sBAAA,CAAAF,OAAA;AACA,IAAAkB,SAAA,GAAAlB,OAAA;AACA,IAAAmB,WAAA,GAAAjB,sBAAA,CAAAF,OAAA;AACA,IAAAoB,KAAA,GAAApB,OAAA;AACA,IAAAqB,OAAA,GAAArB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAsB,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,KAAA,GACA,eACA,cACA,cACA,iBACA,kBACA,eACA,oBACA,eACA,kBACA,iBACA,cACA,eACA,sBACA,qBACA,mBACA,uBACA;EACAC,MAAA;IACAC,SAAA;MACAC,OAAA;QAAAC,KAAA;MAAA;IACA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;MACAP,OAAA;IACA;IACAQ,QAAA,EAAAC,QAAA;IACAC,QAAA;MACAN,IAAA,EAAAO,OAAA;MACAX,OAAA;IACA;IACAY,QAAA;MACAR,IAAA,EAAAO,OAAA;MACAX,OAAA;IACA;IACAa,SAAA;MACAT,IAAA,EAAAU,KAAA;MACAd,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAe,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MACAC,iBAAA;MACA;MACAC,IAAA;MACA;MACAC,YAAA;QACAC,OAAA;MACA;MACAC,WAAA;MACAC,EAAA,EAAAC,uBAAA;MACAC,cAAA,EAAAC,YAAA,CAAAC,OAAA;MACAC,YAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,IAAA;QACA;QACA;QACA;QACA;MACA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;MACAC,wBAAA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,WAAA;IACA,KAAAC,cAAA;IACA,KAAAC,oBAAA;IACA,KAAAC,cAAA;IACA,KAAAC,SAAA;MACA,IAAAN,KAAA,CAAAjB,cAAA;QACAiB,KAAA,CAAAd,YAAA;QACAc,KAAA,CAAAb,iBAAA;MACA;MACAa,KAAA,CAAAO,KAAA;MACAP,KAAA,CAAAQ,IAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,iBAAAC,EAAA;QAAA,IAAAC,qBAAA;QAAA,SAAAA,qBAAA,GAAAF,MAAA,CAAAjB,aAAA,CAAAoB,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAxD,OAAA,KAAAqD,EAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAG,SAAA;MAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,iBAAAN,EAAA;QAAA,IAAAO,qBAAA;QAAA,SAAAA,qBAAA,GAAAD,MAAA,CAAAxB,aAAA,CAAAoB,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAxD,OAAA,KAAAqD,EAAA;QAAA,gBAAAO,qBAAA,uBAAAA,qBAAA,CAAAC,EAAA;MAAA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,GAAA,OAAAC,mBAAA,CAAApE,OAAA,MAAAqE,GAAA,CACA,MAAAhD,IAAA,CAAAiD,OAAA,QACAC,KAAA,MACAC,MAAA,CAAA7D,OAAA,CACA;MAEA,OAAAwD,GAAA,CAAAM,GAAA,WAAAjB,EAAA;QACA,IAAAkB,IAAA,GAAAR,MAAA,CAAAlC,WAAA,CAAA0B,IAAA,WAAAiB,CAAA;UAAA,OACArE,MAAA,CAAAqE,CAAA,CAAAC,MAAA,MAAAtE,MAAA,CAAAkD,EAAA;QAAA,CACA;QACA;UACAA,EAAA,EAAAA,EAAA;UACA3E,IAAA,GAAA6F,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAG,QAAA;UACAC,KAAA,GAAAJ,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAK,WAAA;QACA;MACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,IAAAd,GAAA,OAAAC,mBAAA,CAAApE,OAAA,MAAAqE,GAAA,CACA,MAAAhD,IAAA,CAAA6D,OAAA,QACAX,KAAA,MACAC,MAAA,CAAA7D,OAAA,CACA;MAEA,OAAAwD,GAAA,CAAAM,GAAA,WAAAjB,EAAA;QACA,IAAAkB,IAAA,GAAAO,MAAA,CAAA/C,WAAA,CAAAwB,IAAA,WAAAiB,CAAA;UAAA,OACArE,MAAA,CAAAqE,CAAA,CAAAnB,EAAA,MAAAlD,MAAA,CAAAkD,EAAA;QAAA,CACA;QACA;UACAA,EAAA,EAAAA,EAAA;UACA3E,IAAA,GAAA6F,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAS,UAAA;QACA;MACA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA,UAAA/D,YAAA,CAAAgE,YAAA;MACA,YAAAhE,YAAA,CAAAgE,YAAA,CAAAf,KAAA,MACAE,GAAA,WAAAc,GAAA;QAAA,IAAAC,qBAAA;QAAA,SAAAA,qBAAA,GAAAH,MAAA,CAAAI,IAAA,CAAArF,IAAA,gBAAAsD,IAAA,WAAAgC,CAAA;UAAA,OAAAA,CAAA,CAAAzF,KAAA,KAAAsF,GAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAG,KAAA,KAAAJ,GAAA;MAAA;IACA;EACA;EACAK,SAAA,WAAAA,UAAA;IAAA,IAAAC,MAAA;IACA,KAAA3C,SAAA;MACA2C,MAAA,CAAA1C,KAAA;MACA0C,MAAA,CAAAzC,IAAA;IACA;EACA;EACA0C,KAAA;IACA1E,iBAAA;MACA2E,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,MAAA;UACAF,MAAA,CAAAG,OAAA,WAAAC,IAAA,EAAAC,KAAA;YACA,IAAAC,MAAA,CAAAC,IAAA,CAAAH,IAAA,EAAAF,MAAA;cACAE,IAAA,CAAAI,MAAA,OAAAC,yBAAA;YACA;UACA;QACA;MACA;IACA;IACA;MACAV,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,IAAAD,MAAA;UACA,KAAA3E,IAAA,CAAAqF,UAAA,GAAAV,MAAA,CAAAW,QAAA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,SAAA;MACA,IAAAA,SAAA,CAAAC,QAAA,CAAAH,KAAA,CAAAI,QAAA;QACA,YAAA7F,IAAA,CAAAyF,KAAA,CAAAI,QAAA;MACA;MACA,IAAAJ,KAAA,CAAAI,QAAA;QAAA,IAAAC,qBAAA;QACA,SAAAA,qBAAA,QAAA1B,IAAA,CAAArF,IAAA,CAAAgH,oBAAA,CAAA1D,IAAA,WAAAgC,CAAA;UAAA,OAAAA,CAAA,CAAAzF,KAAA,KAAA8G,MAAA,CAAA1F,IAAA,CAAAyF,KAAA,CAAAI,QAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAxB,KAAA,UAAAtE,IAAA,CAAAyF,KAAA,CAAAI,QAAA;MACA;MACA,YAAA7F,IAAA,CAAAyF,KAAA,CAAAI,QAAA;IACA;IAEAG,YAAA,WAAAA,aAAAP,KAAA;MACA,IAAAQ,cAAA;MACA,IAAAA,cAAA,CAAAL,QAAA,CAAAH,KAAA,CAAAI,QAAA;MACA;MACA;IACA;IACA;IACAK,eAAA,WAAAA,gBAAAT,KAAA;MACA,IAAAA,KAAA,CAAAI,QAAA;QACA,YAAA7F,IAAA,CAAAqF,UAAA;MACA;MACA,IAAAI,KAAA,CAAAI,QAAA;QACA,YAAA7F,IAAA,CAAAmG,OAAA;MACA;MACA,IAAAV,KAAA,CAAAI,QAAA;QACA,YAAA7F,IAAA,CAAAoG,QAAA;MACA;MACA,IAAAX,KAAA,CAAAI,QAAA;QACA,YAAAvF,cAAA;MACA;MACA;IACA;IAEA+F,cAAA,WAAAA,eAAAR,QAAA;MACA,IAAAS,OAAA;QACAjB,UAAA;QACAkB,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,MAAA;QACAC,KAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,gBAAA;MACA;MACA,YAAA3C,IAAA,CAAArF,IAAA,CAAAuH,OAAA,CAAAT,QAAA;IACA;IAEArE,gBAAA,WAAAA,iBAAA;MAAA,IAAAwF,MAAA;MACA,IAAAC,yBAAA;QAAA;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAA/F,aAAA,GAAAkG,GAAA,CAAAzH,IAAA;MACA;IACA;IACA,UACAqC,IAAA,WAAAA,KAAA;MAAA,IAAAqF,OAAA;MAAA,WAAAC,kBAAA,CAAA1I,OAAA,mBAAA2I,oBAAA,CAAA3I,OAAA,IAAA4I,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAA3I,OAAA,IAAA8I,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,KAEAT,OAAA,CAAAtI,OAAA;gBAAA6I,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,2BAAA,EAAAV,OAAA,CAAAtI,OAAA,EAAAoI,IAAA,WAAAa,QAAA;gBACA;gBACAX,OAAA,CAAApH,IAAA,CAAAlB,OAAA,GAAAsI,OAAA,CAAAtI,OAAA;gBACAsI,OAAA,CAAApH,IAAA,GAAA+H,QAAA,CAAArI,IAAA,CAAAsI,aAAA;gBACA,IAAAC,mBAAA;kBAAA,WAAA5H,uBAAA,UAAA+G,OAAA,CAAAc,KAAA;gBAAA,GAAAhB,IAAA,WAAAiB,IAAA;kBACA,KAAAA,IAAA;oBACA;kBACA;kBACA,IAAAA,IAAA,YAAA1I,KAAA;oBACA0I,IAAA,CAAArD,OAAA,WAAAC,IAAA;sBAAA,OAAAA,IAAA,CAAAqD,OAAA;oBAAA;kBACA;oBACAD,IAAA,CAAAC,OAAA;kBACA;gBACA;gBACA;gBACAhB,OAAA,CAAAnH,YAAA,CAAAnB,OAAA,GAAAsI,OAAA,CAAAtI,OAAA;gBACAsI,OAAA,CAAAnH,YAAA,GAAA8H,QAAA,CAAArI,IAAA,CAAA2I,sBAAA;gBACAjB,OAAA,CAAAnH,YAAA,CAAAqI,QAAA,GAAAlB,OAAA,CAAAnH,YAAA,CAAAqI,QAAA,YAAAlB,OAAA,CAAAnH,YAAA,CAAAqI,QAAA;gBACAlB,OAAA,CAAAnH,YAAA,CAAAsI,iBAAA,GAAAnB,OAAA,CAAAnH,YAAA,CAAAsI,iBAAA,YAAAnB,OAAA,CAAAnH,YAAA,CAAAsI,iBAAA;gBACAnB,OAAA,CAAAnH,YAAA,CAAAuI,kBAAA,GAAApB,OAAA,CAAAnH,YAAA,CAAAuI,kBAAA,YAAApB,OAAA,CAAAnH,YAAA,CAAAuI,kBAAA;gBACApB,OAAA,CAAArH,iBAAA,GAAAgI,QAAA,CAAArI,IAAA,CAAA2I,sBAAA,CAAAI,aAAA;gBACA,IAAArB,OAAA,CAAArH,iBAAA,CAAA8E,MAAA;kBACA,IAAA6D,CAAA;kBACA,OAAAA,CAAA,OAAAtB,OAAA,CAAArH,iBAAA,CAAA8E,MAAA;oBACAuC,OAAA,CAAArH,iBAAA,CAAA4I,IAAA;kBACA;gBACA;cACA,GAAAC,OAAA;gBACAxB,OAAA,CAAAjG,SAAA;cACA;YAAA;cAAAwG,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEAT,OAAA,CAAAjG,SAAA;YAAA;YAAA;cAAA,OAAAwG,QAAA,CAAAkB,IAAA;UAAA;QAAA,GAAArB,OAAA;MAAA;IAEA;IAGA,WACA1F,KAAA,WAAAA,MAAA;MACA,KAAA9B,IAAA;QACAlB,OAAA,EAAAgK,SAAA;QACAC,SAAA,EAAAD,SAAA;QACAvG,SAAA,EAAAuG,SAAA;QACAE,eAAA,EAAAF,SAAA;QACAG,gBAAA,EAAAH,SAAA;QACA7F,OAAA,EAAA6F,SAAA;QACAI,SAAA,EAAAJ,SAAA;QACAzD,UAAA,EAAAyD,SAAA;QACArF,KAAA,EAAAqF,SAAA;QACAK,SAAA,EAAAL,SAAA;QACAM,aAAA,EAAAN,SAAA;QACAO,UAAA,EAAAP,SAAA;QACAQ,cAAA,EAAAR,SAAA;QACAvC,SAAA,EAAAuC,SAAA;QACAS,OAAA,EAAAT,SAAA;QACAU,OAAA,EAAAV,SAAA;QACA7E,YAAA,EAAA6E,SAAA;QACAW,SAAA,EAAAX,SAAA;QACAY,UAAA,EAAAZ,SAAA;QACAa,SAAA,EAAAb,SAAA;QACAc,UAAA,EAAAd,SAAA;QACAe,GAAA,EAAAf,SAAA;QACAgB,GAAA,EAAAhB,SAAA;QACArC,SAAA,EAAAqC,SAAA;QACApC,MAAA,EAAAoC,SAAA;QACAiB,OAAA,EAAAjB,SAAA;QACAkB,MAAA,EAAAlB,SAAA;QACAmB,KAAA,EAAAnB,SAAA;QACAoB,OAAA,EAAApB,SAAA;QACAqB,MAAA;QACAC,MAAA,EAAAtB,SAAA;QACAuB,MAAA,EAAAvB,SAAA;QACAwB,KAAA,EAAAxB,SAAA;QACAyB,MAAA,EAAAzB,SAAA;QACA3C,OAAA,EAAA2C,SAAA;QACA1C,QAAA,EAAA0C,SAAA;QACA0B,SAAA,EAAA1B,SAAA;QACA2B,UAAA,EAAA3B,SAAA;QACA4B,QAAA,EAAA5B,SAAA;QACA6B,MAAA,EAAA7B,SAAA;QACAvF,MAAA,EAAAuF,SAAA;QACA8B,MAAA,EAAA9B,SAAA;QACA+B,MAAA,EAAA/B,SAAA;QACAjF,OAAA,EAAAiF,SAAA;QACAgC,MAAA,EAAAhC,SAAA;QACAiC,IAAA,EAAAjC,SAAA;QACAkC,SAAA,EAAAlC,SAAA;QACAmC,QAAA,EAAAnC,SAAA;QACAoC,QAAA,EAAApC,SAAA;QACAqC,OAAA,EAAArC,SAAA;QACAsC,OAAA,EAAAtC,SAAA;QACAuC,IAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACA,KAAAtL,YAAA;QACAuL,gBAAA,EAAA1C,SAAA;QACAR,QAAA,EAAAQ,SAAA;QACAP,iBAAA,EAAAO,SAAA;QACAN,kBAAA,EAAAM;MACA;MACA,KAAA2C,SAAA;MACA,KAAAA,SAAA;IACA;IAEAC,YAAA,WAAAA,aAAAhM,IAAA;MACA,IAAAA,IAAA;QACA,KAAAiM,IAAA,MAAA3L,IAAA,uBAAAN,IAAA,CAAA0D,GAAA,WAAA2B,IAAA;UAAA,OAAAA,IAAA,CAAA6G,QAAA;QAAA;MACA;IACA;IAEA,aACAnK,WAAA,WAAAA,YAAA;MAAA,IAAAoK,OAAA;MACA,SAAAnN,SAAA,CAAAE,KAAA;QACA,IAAAkN,oBAAA,IAAA5E,IAAA,WAAAa,QAAA;UACA8D,OAAA,CAAA1L,WAAA,GAAA4H,QAAA,CAAArI,IAAA;QACA;MACA;QACA,IAAAqM,oBAAA,IAAA7E,IAAA,WAAAa,QAAA;UACA8D,OAAA,CAAA1L,WAAA,GAAA4H,QAAA,CAAArI,IAAA;QACA;MACA;IACA;IAEA;IACAgC,cAAA,WAAAA,eAAA;MAAA,IAAAsK,OAAA;MACA,IAAAC,cAAA;QACAC,KAAA;QACAC,aAAA;QACAC,SAAA;QACAC,QAAA;QACA7I,QAAA;QACAE,WAAA;MACA,GAAAwD,IAAA,WAAAa,QAAA;QACAiE,OAAA,CAAArL,WAAA,GAAAoH,QAAA,CAAAuE,IAAA;MACA;IACA;IAEA,cACA3K,oBAAA,WAAAA,qBAAA;MAAA,IAAA4K,OAAA;MACA,IAAAC,kBAAA,IAAAtF,IAAA,WAAAa,QAAA;QACAwE,OAAA,CAAA3L,oBAAA,GAAAmH,QAAA,CAAArI,IAAA;MACA;IACA;IAEA,cACAkC,cAAA,WAAAA,eAAA;MAAA,IAAA6K,OAAA;MACA,IAAAC,+BAAA;QACAC,aAAA,OAAA7N,OAAA;QACA8N,eAAA,OAAA5M,IAAA,CAAA6D,OAAA;QACAqI,KAAA;QACAC,aAAA;QACAU,OAAA;QACAC,QAAA;QACAC,UAAA;QACAjJ,UAAA;QACAkJ,gBAAA;MACA,GAAA9F,IAAA,WAAAa,QAAA;QACA0E,OAAA,CAAA5L,WAAA,GAAAkH,QAAA,CAAAuE,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}
package com.ruoyi.ffsafe.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.ffsafe.api.domain.FfsafeFlowDetail;
import com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper;
import com.ruoyi.ffsafe.api.service.IFfsafeFlowDetailService;
import com.ruoyi.safe.domain.TblBusinessApplication;
import com.ruoyi.safe.domain.TblNetworkIpMac;
import com.ruoyi.safe.mapper.TblBusinessApplicationMapper;
import com.ruoyi.safe.service.ITblNetworkIpMacService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 非凡流量详情日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class FfsafeFlowDetailServiceImpl implements IFfsafeFlowDetailService {
    @Autowired
    private FfsafeFlowDetailMapper ffsafeFlowDetailMapper;
    @Resource
    private ITblNetworkIpMacService tblNetworkIpMacService;
    @Resource
    private TblBusinessApplicationMapper businessApplicationMapper;

    /**
     * 查询非凡流量详情日志
     *
     * @param id 非凡流量详情日志主键
     * @return 非凡流量详情日志
     */
    @Override
    public FfsafeFlowDetail selectFfsafeFlowDetailById(Long id)
    {
        return ffsafeFlowDetailMapper.selectFfsafeFlowDetailById(id);
    }

    /**
     * 批量查询非凡流量详情日志
     *
     * @param ids 非凡流量详情日志主键集合
     * @return 非凡流量详情日志集合
     */
    @Override
    public List<FfsafeFlowDetail> selectFfsafeFlowDetailByIds(Long[] ids)
    {
        return ffsafeFlowDetailMapper.selectFfsafeFlowDetailByIds(ids);
    }

    /**
     * 查询非凡流量详情日志列表
     *
     * @param ffsafeFlowDetail 非凡流量详情日志
     * @return 非凡流量详情日志
     */
    @Override
    public List<FfsafeFlowDetail> selectFfsafeFlowDetailList(FfsafeFlowDetail ffsafeFlowDetail)
    {
        List<FfsafeFlowDetail> ffsafeFlowDetails = ffsafeFlowDetailMapper.selectFfsafeFlowDetailList(ffsafeFlowDetail);
        /*List<String> ipList = new ArrayList<>();
        ffsafeFlowDetails.forEach(item->{
            if(StrUtil.isNotBlank(item.getDip())){
                ipList.add(item.getDip());
            }
            if(StrUtil.isNotBlank(item.getSip())){
                ipList.add(item.getSip());
            }
        });
        if(CollUtil.isNotEmpty(ipList)){
            List<TblNetworkIpMac> networkIpMacList = tblNetworkIpMacService.selectListByIps(CollUtil.distinct(ipList));
            List<Long> networkIds = networkIpMacList.stream().map(TblNetworkIpMac::getId).collect(Collectors.toList());
            List<TblBusinessApplication> businessApplications = new ArrayList<>();
            if(CollUtil.isNotEmpty(networkIds)){
                businessApplications = businessApplicationMapper.selectListByNetworkIpMacIds(networkIds);
            }
            List<TblBusinessApplication> finalBusinessApplications = businessApplications;
            ffsafeFlowDetails.forEach(item->{
                if(CollUtil.isNotEmpty(networkIpMacList)){
                    if(StrUtil.isNotBlank(item.getSip())){
                        networkIpMacList.stream().filter(networkIpMac -> item.getSip().equals(networkIpMac.getIpv4()) && networkIpMac.getAssetId()!=null)
                                .findFirst().ifPresent(networkIpMac -> item.setSipIsAsset(true));
                    }
                    if(StrUtil.isNotBlank(item.getDip())){
                        networkIpMacList.stream().filter(networkIpMac -> item.getDip().equals(networkIpMac.getIpv4()) && networkIpMac.getAssetId()!=null)
                                .findFirst().ifPresent(networkIpMac -> item.setDipIsAsset(true));
                    }
                }
                if(CollUtil.isNotEmpty(finalBusinessApplications)){
                    if(StrUtil.isNotBlank(item.getSip()) || StrUtil.isNotBlank(item.getDip())){
                        List<TblBusinessApplication> matchApplication = finalBusinessApplications.stream().filter(businessApplication -> {
                            if ((StrUtil.isNotBlank(item.getSip()) && item.getSip().equals(businessApplication.getAssetIp())) || (StrUtil.isNotBlank(item.getDip()) && item.getDip().equals(businessApplication.getAssetIp()))) {
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
                        item.setBusinessApplications(matchApplication);
                    }
                }
            });

        }*/
        return ffsafeFlowDetails;
    }

    /**
     * 新增非凡流量详情日志
     *
     * @param ffsafeFlowDetail 非凡流量详情日志
     * @return 结果
     */
    @Override
    public int insertFfsafeFlowDetail(FfsafeFlowDetail ffsafeFlowDetail)
    {
        ffsafeFlowDetail.setCreateTime(DateUtils.getNowDate());
        return ffsafeFlowDetailMapper.insertFfsafeFlowDetail(ffsafeFlowDetail);
    }

    /**
     * 修改非凡流量详情日志
     *
     * @param ffsafeFlowDetail 非凡流量详情日志
     * @return 结果
     */
    @Override
    public int updateFfsafeFlowDetail(FfsafeFlowDetail ffsafeFlowDetail)
    {
        return ffsafeFlowDetailMapper.updateFfsafeFlowDetail(ffsafeFlowDetail);
    }

    /**
     * 删除非凡流量详情日志信息
     *
     * @param id 非凡流量详情日志主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeFlowDetailById(Long id)
    {
        return ffsafeFlowDetailMapper.deleteFfsafeFlowDetailById(id);
    }

    /**
     * 批量删除非凡流量详情日志
     *
     * @param ids 需要删除的非凡流量详情日志主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeFlowDetailByIds(Long[] ids)
    {
        return ffsafeFlowDetailMapper.deleteFfsafeFlowDetailByIds(ids);
    }

    @Override
    public List<FfsafeFlowDetail> selectList(FfsafeFlowDetail queryFlowDetail) {
        return ffsafeFlowDetailMapper.selectList(queryFlowDetail);
    }

    @Override
    public List<JSONObject> groupThreatenName(FfsafeFlowDetail query) {
        return ffsafeFlowDetailMapper.groupThreatenName(query);
    }

    @Override
    public List<JSONObject> groupDip(FfsafeFlowDetail query) {
        return ffsafeFlowDetailMapper.groupDip(query);
    }

    @Override
    public long count(FfsafeFlowDetail queryFlowDetail) {
        return ffsafeFlowDetailMapper.count(queryFlowDetail);
    }
}

package com.ruoyi.safe.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult;
import com.ruoyi.safe.domain.TblServer;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.safe.domain.dto.QueryDeptServerCountDto;
import com.ruoyi.safe.dto.TblServerImportDTO;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * 服务器/存储设备Service接口
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface ITblServerService
{
    /**
     * 查询服务器/存储设备
     *
     * @param assetId 服务器/存储设备主键
     * @return 服务器/存储设备
     */
    public TblServer selectTblServerByAssetId(Long assetId);

    /**
     * 查询服务器/存储设备列表
     *
     * @param assetIds 服务器/存储设备主键
     * @return 服务器/存储设备集合
     */
    public List<TblServer> selectTblServerByAssetIds(Long[] assetIds);

    /**
     * 查询服务器/存储设备列表
     *
     * @param tblServer 服务器/存储设备
     * @return 服务器/存储设备集合
     */
    public List<TblServer> selectTblServerList(TblServer tblServer);
    public List<TblServer> selectTblServerList2(TblServer tblServer);
    int countNum();
    /**
     * 新增服务器/存储设备
     *
     * @param tblServer 服务器/存储设备
     * @return 结果
     */
    public int insertTblServer(TblServer tblServer);

    public int insertTblServer2(TblServer tblServer);

    /**
     * 修改服务器/存储设备
     *
     * @param tblServer 服务器/存储设备
     * @return 结果
     */
    public int updateTblServer(TblServer tblServer);

    public int updateTblServer2(TblServer tblServer);

    /**
     * 批量删除服务器/存储设备
     *
     * @param assetIds 需要删除的服务器/存储设备主键集合
     * @return 结果
     */
    public int deleteTblServerByAssetIds(Long[] assetIds);

    /**
     * 删除服务器/存储设备信息
     *
     * @param assetId 服务器/存储设备主键
     * @return 结果
     */
    public int deleteTblServerByAssetId(Long assetId);

    /**
     * 导入 服务器
     * <AUTHOR>
     * @Description //
     * @Date 2023/9/12 15:42
     * @param server
     * @return java.lang.String
     **/
    String importServer(List<TblServer> server);

    /**
     * 导入服务器数据（基于简化模板）
     * 使用TblServerImportDTO格式进行服务器数据导入，支持14个核心字段
     * 包含特殊字段处理逻辑：设备厂商/供应商自动新增、业务系统关联、
     * IP匹配网络区域、MAC地址保存、操作系统/数据库处理、部门验证
     *

     * @Description 基于TblServerImportDTO的服务器数据导入功能
     * @Date 2025-01-11
     * @param dtoList TblServerImportDTO列表，包含14个核心导入字段
     * @return String 导入结果信息，包含成功数量和错误信息
     * @throws Exception 当部门不存在、数据验证失败或数据库操作异常时抛出
     */
    String importServerFromTemplate(List<TblServerImportDTO> dtoList) throws Exception;

    /**
     * 资产选择通用组件查询
     */
    List<TblServer> assetSelectByServer(HashMap<String,String> params);

    /**
     * 资产选择通用组件查询
     */
    List<TblServer> assetSelectByServer2(HashMap<String,String> params);

    List<JSONObject> selectTblServerLocationIdIsNotNull();

    int unbind(TblServer tblServer);

    List<JSONObject> unionAssetListByCondition(HashMap<String,String> params);

    public EdrDetailsResult selectEdrByAssetId(Long assetId) throws IOException;

    /**
     * 获取部门服务器统计
     * 
     * @param queryCountDto 查询参数
     * @return 部门树结构列表
     */
    List<TreeSelect> getDeptServerCount(QueryDeptServerCountDto queryCountDto);
}

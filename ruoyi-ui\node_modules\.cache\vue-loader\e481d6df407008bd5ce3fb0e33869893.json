{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\OperationSystemDetails.vue", "mtime": 1754969114059}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Z2V0QXBwbGljYXRpb259IGZyb20gIkAvYXBpL3NhZmUvYXBwbGljYXRpb24iOwppbXBvcnQgQXBwbGljYXRpb25MaW5rIGZyb20gJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYXBwbGljYXRpb25MaW5rJzsKaW1wb3J0IEFwcGxpY2F0aW9uU2l0ZSBmcm9tICdAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FwcGxpY2F0aW9uU2l0ZSc7CmltcG9ydCBVc2VyU2VsZWN0IGZyb20gJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvdXNlclNlbGVjdCc7CmltcG9ydCBEZXB0U2VsZWN0IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvZGVwdFNlbGVjdCc7CmltcG9ydCBOZXR3b3JrU2VsZWN0IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvbmV0d29ya1NlbGVjdCc7CmltcG9ydCBEeW5hbWljVGFnIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljVGFnJzsKaW1wb3J0IFZlbmRvclNlbGVjdDIgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC92ZW5kb3JTZWxlY3QyJzsKaW1wb3J0IERpY3RTZWxlY3QgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC9kaWN0U2VsZWN0JzsKaW1wb3J0IHtnZXRWYWxGcm9tT2JqZWN0fSBmcm9tICJAL3V0aWxzIjsKaW1wb3J0IHtnZW5lcmF0ZVNlY3VyZVVVSUQsIHdhaXRGb3JWYWx1ZX0gZnJvbSAiQC91dGlscy9ydW95aSI7CmltcG9ydCB7bGlzdFZlbmRvckJ5QXBwbGljYXRpb259IGZyb20gIkAvYXBpL3NhZmUvdmVuZG9yIjsKaW1wb3J0IHNlcnZlckVWIGZyb20gIkAvdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYXBwbGljYXRpb25IYXJkd2FyZS9zZXJ2ZXJFVi52dWUiOwppbXBvcnQgZGF0ZUVWIGZyb20gIkAvdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYXBwbGljYXRpb25IYXJkd2FyZS9kYXRlRVYudnVlIjsKaW1wb3J0IG5ldHdvcmtFViBmcm9tICJAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FwcGxpY2F0aW9uSGFyZHdhcmUvbmV0d29ya0VWLnZ1ZSI7CmltcG9ydCBzYWZlRVYgZnJvbSAiQC92aWV3cy9oaGxDb2RlL2NvbXBvbmVudC9hcHBsaWNhdGlvbi9hcHBsaWNhdGlvbkhhcmR3YXJlL3NhZmVFVi52dWUiOwppbXBvcnQgb3ZlclZpZXdTZWxlY3QgZnJvbSAiQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC9vdmVyVmlld1NlbGVjdC52dWUiOwppbXBvcnQge2xpc3RBbGxPdmVydmlld30gZnJvbSAiQC9hcGkvc2FmZS9vdmVydmlldyI7CmltcG9ydCBFZGl0U2VydmVyIGZyb20gIkAvdmlld3Mvc2FmZS9zZXJ2ZXIvZWRpdFNlcnZlci52dWUiOwppbXBvcnQge2dldEFsbERlcHRUcmVlLCBkZXB0VHJlZVNlbGVjdCwgbGlzdFVzZXJ9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIgppbXBvcnQge2xpc3REb21haW59IGZyb20gIkAvYXBpL2RpY3QvZG9tYWluIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiT3BlcmF0aW9uU3lzdGVtRGV0YWlscyIsCiAgY29tcG9uZW50czogewogICAgRWRpdFNlcnZlciwKICAgIG92ZXJWaWV3U2VsZWN0LAogICAgc2FmZUVWLAogICAgbmV0d29ya0VWLAogICAgZGF0ZUVWLAogICAgc2VydmVyRVYsCiAgICBBcHBsaWNhdGlvbkxpbmssCiAgICBBcHBsaWNhdGlvblNpdGUsCiAgICBVc2VyU2VsZWN0LAogICAgRGVwdFNlbGVjdCwKICAgIE5ldHdvcmtTZWxlY3QsCiAgICBEaWN0U2VsZWN0LAogICAgRHluYW1pY1RhZywKICAgIFZlbmRvclNlbGVjdDIsCiAgfSwKICBkaWN0czogWwogICAgJ3NlcnZlX2dyb3VwJywKICAgICdjb3Zlcl9hcmVhJywKICAgICdzeXNfeWVzX25vJywKICAgICdhcHBfbmV0X3NjYWxlJywKICAgICdjb25zdHJ1Y3RfdHlwZScsCiAgICAnc3lzdGVtX3R5cGUnLAogICAgJ3Byb3RlY3Rpb25fZ3JhZGUnLAogICAgJ2Fzc2V0X3N0YXRlJywKICAgICdhcHBfbG9naW5fdHlwZScsCiAgICAnYXBwX3RlY2huaWNhbCcsCiAgICAnYXBwX2RlcGxveScsCiAgICAnYXBwX3N0b3JhZ2UnLAogICAgJ2V2YWx1YXRpb25fcmVzdWx0cycsCiAgICAnZXZhbHVhdGlvbl9zdGF0dXMnLAogICAgJ2lzX29wZW5fbmV0d29yaycsCiAgICAnaHdfaXNfdHJ1ZV9zaHV0X2Rvd24nCiAgXSwKICBpbmplY3Q6IHsKICAgICRlZGl0YWJsZTogewogICAgICBkZWZhdWx0OiB7dmFsdWU6IHRydWV9LAogICAgfQogIH0sCiAgcHJvcHM6IHsKICAgIGFzc2V0SWQ6IHsKICAgICAgdHlwZTogW1N0cmluZywgTnVtYmVyXSwKICAgICAgcmVxdWlyZWQ6IGZhbHNlLAogICAgICBkZWZhdWx0OiBudWxsLAogICAgfSwKICAgIGNoYW5nZUlkOiBGdW5jdGlvbiwKICAgIHJlYWRvbmx5OiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICAgIGRpc2FibGVkOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICAgIGFzc2V0TGlzdDogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogKCkgPT4gW10KICAgIH0sCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGNvbGxhcHNlTmFtZXM6IFsnMScsICcyJywgJzMnLCAnNCcsICc1J10sCiAgICAgIHZlbmRvcnNkYXRhOiAnMScsCiAgICAgIHVzZXJkYXRhOiAnMScsCiAgICAgIGZ1bmN0aW9uU3RhdGVMaXN0OiBbe30sIHt9LCB7fV0sCiAgICAgIC8vIOWfuuacrOS/oeaBr+ihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g5Lia5Yqh5L+h5oGv6KGo5Y2V5Y+C5pWwCiAgICAgIGJ1c2luZXNzRm9ybTogewogICAgICAgIGRlbExpc3Q6IFtdCiAgICAgIH0sCiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgZ3Y6IGdldFZhbEZyb21PYmplY3QsCiAgICAgIGRlcGxveUxvY2F0aW9uOiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgiZGwiKSwKICAgICAgbWFuYWdlckxhYmVsOiAn6LSj5Lu75Lq6L+eUteivnScsCiAgICAgIG1hbmFnZVBsYWNlaG9sZGVyOiAn6K+36YCJ5oup6LSj5Lu75Lq6JywKICAgICAgbWFuYWdlckRhdGE6IFtdLAogICAgICBuZXR3b3JrRG9tYWluT3B0aW9uczogW10sCiAgICAgIHZlbmRvcnNEYXRhOiBbXSwKICAgICAgcmVmczogewogICAgICAgICduZXR3b3JrRVYnOiAi5omA5a6J6KOF5pyN5Yqh5Zmo546v5aKDIiwKICAgICAgICAnc2FmZUVWJzogJ+aJgOWuieijheaVsOaNrueOr+WigycsCiAgICAgICAgJ3NlcnZlckVWJzogJ+WFs+iBlOe9kee7nOiuvuWkhycsCiAgICAgICAgJ2RhdGVFVic6ICLlhbPogZTlronlhajorr7lpIciCiAgICAgIH0sCiAgICAgIGNvbGxhcHNlOiBbJzEnLCAnMicsICczJywgJzQnXSwKICAgICAgc2hvd0FkZFNlcnZlcjogZmFsc2UsCiAgICAgIHNlcnZlck9wdGlvbnM6IFtdLAogICAgICBjdXJyZW50QXNzb2NpYXRpb25TZXJ2ZXI6IFtdLAogICAgICBhZnRlckluaXQ6IGZhbHNlLAogICAgICB1cGxvYWRUeXBlOiBbJ3dhaXRpbmdJbnN1cmFuY2VGaWxpbmdTY2FuJywgJ2V2YWx1YXRpb25SZXBvcnQnLCAnbmV0VG9wbycsICdvcGVyYXRlSGFuZGJvb2snXSwKICAgICAgc2VsZWN0VHlwZTogWydzeXN0ZW1UeXBlJywgJ2NvbnN0cnVjdCcsICdsb2dpblR5cGUnLCAndGVjaG5pY2FsJywgJ2RlcGxveScsICdzdGF0ZScsICdwcm90ZWN0R3JhZGUnLCAnZXZhbHVhdGlvblJlc3VsdHMnLCAnZXZhbHVhdGlvblN0YXR1cyddLAogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0QWxsU2VydmVyTGlzdCgpOwogICAgdGhpcy5nZXREZXB0VHJlZSgpOwogICAgdGhpcy5nZXRNYW5hZ2VyTGlzdCgpOwogICAgdGhpcy5nZXROZXR3b3JrRG9tYWluVHJlZSgpOwogICAgdGhpcy5nZXRWZW5kb3JzRGF0YSgpOwogICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICBpZiAodGhpcy5kZXBsb3lMb2NhdGlvbiA9PT0gJ2ZhaXInKSB7CiAgICAgICAgdGhpcy5tYW5hZ2VyTGFiZWwgPSAn6LSj5Lu75rCR6K2mL+eUteivnScKICAgICAgICB0aGlzLm1hbmFnZVBsYWNlaG9sZGVyID0gJ+ivt+mAieaLqei0o+S7u+awkeitpicKICAgICAgfQogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuaW5pdCgpCiAgICB9KTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDojrflj5bmnI3liqHlmajlkI3np7DmmKDlsIQKICAgIGdldFNlcnZlck5hbWUoKSB7CiAgICAgIHJldHVybiAoaWQpID0+IHRoaXMuc2VydmVyT3B0aW9ucy5maW5kKHMgPT4gcy5hc3NldElkID09PSBpZCk/LmFzc2V0TmFtZSB8fCAnJwogICAgfSwKICAgIC8vIOiOt+WPluacjeWKoeWZqElQ5pig5bCECiAgICBnZXRTZXJ2ZXJJcCgpIHsKICAgICAgcmV0dXJuIChpZCkgPT4gdGhpcy5zZXJ2ZXJPcHRpb25zLmZpbmQocyA9PiBzLmFzc2V0SWQgPT09IGlkKT8uaXAgfHwgJycKICAgIH0sCiAgICBwcm9jZXNzZWRNYW5hZ2VyTGlzdCgpIHsKICAgICAgLy8g5Y676YeNCiAgICAgIGNvbnN0IGlkcyA9IFsuLi5uZXcgU2V0KAogICAgICAgICh0aGlzLmZvcm0ubWFuYWdlciB8fCAnJykKICAgICAgICAgIC5zcGxpdCgnLCcpCiAgICAgICAgICAuZmlsdGVyKEJvb2xlYW4pCiAgICAgICldOwoKICAgICAgcmV0dXJuIGlkcy5tYXAoaWQgPT4gewogICAgICAgIGNvbnN0IHVzZXIgPSB0aGlzLm1hbmFnZXJEYXRhLmZpbmQodSA9PgogICAgICAgICAgTnVtYmVyKHUudXNlcklkKSA9PT0gTnVtYmVyKGlkKQogICAgICAgICk7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGlkLAogICAgICAgICAgbmFtZTogdXNlcj8ubmlja05hbWUgfHwgJ+acquefpeeUqOaItycsCiAgICAgICAgICBwaG9uZTogdXNlcj8ucGhvbmVudW1iZXIgfHwgJycKICAgICAgICB9OwogICAgICB9KTsKICAgIH0sCiAgICBwcm9jZXNzZWRWZW5kb3JzTGlzdCgpIHsKICAgICAgY29uc3QgaWRzID0gWy4uLm5ldyBTZXQoCiAgICAgICAgKHRoaXMuZm9ybS52ZW5kb3JzIHx8ICcnKQogICAgICAgICAgLnNwbGl0KCcsJykKICAgICAgICAgIC5maWx0ZXIoQm9vbGVhbikKICAgICAgKV07CgogICAgICByZXR1cm4gaWRzLm1hcChpZCA9PiB7CiAgICAgICAgY29uc3QgdXNlciA9IHRoaXMudmVuZG9yc0RhdGEuZmluZCh1ID0+CiAgICAgICAgICBOdW1iZXIodS5pZCkgPT09IE51bWJlcihpZCkKICAgICAgICApOwogICAgICAgIHJldHVybiB7CiAgICAgICAgICBpZCwKICAgICAgICAgIG5hbWU6IHVzZXI/LnZlbmRvck5hbWUgfHwgJ+acquefpeeUqOaItycsCiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9LAogICAgcHJvY2Vzc2VkU2VydmljZUdyb3VwcygpIHsKICAgICAgaWYgKCF0aGlzLmJ1c2luZXNzRm9ybS5zZXJ2aWNlR3JvdXApIHJldHVybiBbXQogICAgICByZXR1cm4gdGhpcy5idXNpbmVzc0Zvcm0uc2VydmljZUdyb3VwLnNwbGl0KCcsJykKICAgICAgICAubWFwKHZhbCA9PiB0aGlzLmRpY3QudHlwZVsnc2VydmVfZ3JvdXAnXS5maW5kKGQgPT4gZC52YWx1ZSA9PT0gdmFsKT8ubGFiZWwgfHwgdmFsKQogICAgfQogIH0sCiAgYWN0aXZhdGVkKCkgewogICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuaW5pdCgpCiAgICB9KTsKICB9LAogIHdhdGNoOiB7CiAgICBmdW5jdGlvblN0YXRlTGlzdDogewogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7CiAgICAgICAgaWYgKG5ld1ZhbCAmJiBuZXdWYWwubGVuZ3RoID4gMCkgewogICAgICAgICAgbmV3VmFsLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7CiAgICAgICAgICAgIGlmIChPYmplY3Qua2V5cyhpdGVtKS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgaXRlbS50ZW1wSWQgPSBnZW5lcmF0ZVNlY3VyZVVVSUQoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0sCiAgICB9LAogICAgJ2Zvcm0uc3lzdGVtVHlwZSc6IHsKICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIGlmIChuZXdWYWwpIHsKICAgICAgICAgIHRoaXMuZm9ybS5zeXN0ZW1UeXBlID0gbmV3VmFsLnRvU3RyaW5nKCk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogIH0sCiAgbWV0aG9kczogewogICAgZ2V0RmllbGRWYWx1ZShmaWVsZCkgewogICAgICAvLyDlhbbku5bln7rmnKzkv6Hmga/lrZfmrrXmoLzlvI/ljJYKICAgICAgbGV0IGZpbHRlckFyciA9IFsnaXNiYXNlJywgJ2lzbG9nJywgJ2lzYWRhcHQnLCAnaXNjaXBoZXInLCAnaXNwbGFuJywgJ2lzbGluaycsICdpc2tleScsICdpc09wZW5OZXR3b3JrJ10KICAgICAgaWYgKGZpbHRlckFyci5pbmNsdWRlcyhmaWVsZC5maWVsZEtleSkpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtW2ZpZWxkLmZpZWxkS2V5XSA9PT0gJ1knID8gJ+aYrycgOiAn5ZCmJzsKICAgICAgfQogICAgICBpZihmaWVsZC5maWVsZEtleSA9PT0gJ2h3SXNUcnVlU2h1dERvd24nKXsKICAgICAgICByZXR1cm4gdGhpcy5kaWN0LnR5cGUuaHdfaXNfdHJ1ZV9zaHV0X2Rvd24uZmluZChkID0+IGQudmFsdWUgPT09IHRoaXMuZm9ybVtmaWVsZC5maWVsZEtleV0pPy5sYWJlbCB8fCB0aGlzLmZvcm1bZmllbGQuZmllbGRLZXldOwogICAgICB9CiAgICAgIHJldHVybiB0aGlzLmZvcm1bZmllbGQuZmllbGRLZXldOwogICAgfSwKCiAgICBnZXRGaWVsZFNwYW4oZmllbGQpIHsKICAgICAgY29uc3QgZnVsbFNwYW5GaWVsZHMgPSBbJ2Fzc29jaWF0aW9uU2VydmVyJywgJ25ldFRvcG8nLCAnbmV0TWVtbycsICdldmFsdWF0aW9uUmVwb3J0JywgJ3dhaXRpbmdJbnN1cmFuY2VGaWxpbmdTY2FuJ107CiAgICAgIGlmIChmdWxsU3BhbkZpZWxkcy5pbmNsdWRlcyhmaWVsZC5maWVsZEtleSkpIHJldHVybiAzOwogICAgICAvLyDlhbbku5blrZfmrrXpu5jorqTljaA45YiXCiAgICAgIHJldHVybiAxOwogICAgfSwKICAgIC8vIOWIpOaWreWtl+auteaYr+WQpuaYvuekugogICAgc2hvdWxkU2hvd0ZpZWxkKGZpZWxkKSB7CiAgICAgIGlmIChmaWVsZC5maWVsZEtleSA9PT0gJ290aGVyU3lzdGVtTm90ZXMnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS5zeXN0ZW1UeXBlID09PSAnMTInOwogICAgICB9CiAgICAgIGlmIChmaWVsZC5maWVsZEtleSA9PT0gJ2FkYXB0RGF0ZScpIHsKICAgICAgICByZXR1cm4gdGhpcy5mb3JtLmlzYWRhcHQgPT09ICdZJzsKICAgICAgfQogICAgICBpZiAoZmllbGQuZmllbGRLZXkgPT09ICdjaXBoZXJEYXRlJykgewogICAgICAgIHJldHVybiB0aGlzLmZvcm0uaXNjaXBoZXIgPT09ICdZJzsKICAgICAgfQogICAgICBpZiAoZmllbGQuZmllbGRLZXkgPT09ICdpc2xpbmsnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZGVwbG95TG9jYXRpb24gPT09ICdmYWlyJzsKICAgICAgfQogICAgICByZXR1cm4gdHJ1ZTsKICAgIH0sCgogICAgZ2V0RGljdE9wdGlvbnMoZmllbGRLZXkpIHsKICAgICAgY29uc3QgZGljdE1hcCA9IHsKICAgICAgICBzeXN0ZW1UeXBlOiAnc3lzdGVtX3R5cGUnLAogICAgICAgIGNvbnN0cnVjdDogJ2NvbnN0cnVjdF90eXBlJywKICAgICAgICBsb2dpblR5cGU6ICdhcHBfbG9naW5fdHlwZScsCiAgICAgICAgdGVjaG5pY2FsOiAnYXBwX3RlY2huaWNhbCcsCiAgICAgICAgZGVwbG95OiAnYXBwX2RlcGxveScsCiAgICAgICAgc3RhdGU6ICdhc3NldF9zdGF0ZScsCiAgICAgICAgcHJvdGVjdEdyYWRlOiAncHJvdGVjdGlvbl9ncmFkZScsCiAgICAgICAgZXZhbHVhdGlvblJlc3VsdHM6ICdldmFsdWF0aW9uX3Jlc3VsdHMnLAogICAgICAgIGV2YWx1YXRpb25TdGF0dXM6ICdldmFsdWF0aW9uX3N0YXR1cycsCiAgICAgICAgaHdJc1RydWVTaHV0RG93bjogJ2h3X2lzX3RydWVfc2h1dF9kb3duJwogICAgICB9OwogICAgICByZXR1cm4gdGhpcy5kaWN0LnR5cGVbZGljdE1hcFtmaWVsZEtleV1dIHx8IFtdOwogICAgfSwKCiAgICBnZXRBbGxTZXJ2ZXJMaXN0KCkgewogICAgICBsaXN0QWxsT3ZlcnZpZXcoeyJhc3NldENsYXNzIjogNH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnNlcnZlck9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgfSkKICAgIH0sCiAgICAvKiog5Yid5aeL5YyWICovCiAgICBhc3luYyBpbml0KCkgewogICAgICAvLyBsZXQgcGFyYW1zID0gdGhpcy4kcm91dGUucXVlcnk7CiAgICAgIGlmICh0aGlzLmFzc2V0SWQpIHsKICAgICAgICBhd2FpdCBnZXRBcHBsaWNhdGlvbih0aGlzLmFzc2V0SWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgLy8g6I635Y+W5bqU55So5L+h5oGv6K+m5oOFCiAgICAgICAgICB0aGlzLmZvcm0uYXNzZXRJZCA9IHRoaXMuYXNzZXRJZDsKICAgICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGEuYXBwbGljYXRpb25WTzsKICAgICAgICAgIHdhaXRGb3JWYWx1ZSgoKSA9PiBnZXRWYWxGcm9tT2JqZWN0KCdzaXRlJywgdGhpcy4kcmVmcywgbnVsbCkpLnRoZW4oc2l0ZSA9PiB7CiAgICAgICAgICAgIGlmKCFzaXRlKXsKICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYoc2l0ZSBpbnN0YW5jZW9mIEFycmF5KXsKICAgICAgICAgICAgICBzaXRlLmZvckVhY2goaXRlbSA9PiBpdGVtLmdldExpc3QoKSk7CiAgICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgICBzaXRlLmdldExpc3QoKQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgICAgLy8g6I635Y+W5Lia5Yqh5L+h5oGv6K+m5oOFCiAgICAgICAgICB0aGlzLmJ1c2luZXNzRm9ybS5hc3NldElkID0gdGhpcy5hc3NldElkOwogICAgICAgICAgdGhpcy5idXNpbmVzc0Zvcm0gPSByZXNwb25zZS5kYXRhLnRibEJ1c2luZXNzQXBwbGljYXRpb247CiAgICAgICAgICB0aGlzLmJ1c2luZXNzRm9ybS51c2VyTnVtcyA9IHRoaXMuYnVzaW5lc3NGb3JtLnVzZXJOdW1zICE9PSBudWxsID8gdGhpcy5idXNpbmVzc0Zvcm0udXNlck51bXMgKyAnJyA6ICcnOwogICAgICAgICAgdGhpcy5idXNpbmVzc0Zvcm0uZXZlcnlkYXlWaXNpdE51bXMgPSB0aGlzLmJ1c2luZXNzRm9ybS5ldmVyeWRheVZpc2l0TnVtcyAhPT0gbnVsbCA/IHRoaXMuYnVzaW5lc3NGb3JtLmV2ZXJ5ZGF5VmlzaXROdW1zICsgJycgOiAnJzsKICAgICAgICAgIHRoaXMuYnVzaW5lc3NGb3JtLmV2ZXJ5ZGF5QWN0aXZlTnVtcyA9IHRoaXMuYnVzaW5lc3NGb3JtLmV2ZXJ5ZGF5QWN0aXZlTnVtcyAhPT0gbnVsbCA/IHRoaXMuYnVzaW5lc3NGb3JtLmV2ZXJ5ZGF5QWN0aXZlTnVtcyArICcnIDogJyc7CiAgICAgICAgICB0aGlzLmZ1bmN0aW9uU3RhdGVMaXN0ID0gcmVzcG9uc2UuZGF0YS50YmxCdXNpbmVzc0FwcGxpY2F0aW9uLnRibE1hcHBlckxpc3QgfHwgW3t9LCB7fSwge31dOwogICAgICAgICAgaWYgKHRoaXMuZnVuY3Rpb25TdGF0ZUxpc3QubGVuZ3RoIDwgMykgewogICAgICAgICAgICBsZXQgaSA9IDA7CiAgICAgICAgICAgIHdoaWxlIChpIDwgMyAtIHRoaXMuZnVuY3Rpb25TdGF0ZUxpc3QubGVuZ3RoKSB7CiAgICAgICAgICAgICAgdGhpcy5mdW5jdGlvblN0YXRlTGlzdC5wdXNoKHt9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgICAgdGhpcy5hZnRlckluaXQgPSB0cnVlOwogICAgICAgIH0pCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5hZnRlckluaXQgPSB0cnVlOwogICAgICB9CiAgICB9LAoKCiAgICAvKiog6KGo5Y2V6YeN572uICovCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGFzc2V0SWQ6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldENvZGU6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldE5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBzb2Z0d2FyZVZlcnNpb246IHVuZGVmaW5lZCwKICAgICAgICBkZWdyZWVJbXBvcnRhbmNlOiB1bmRlZmluZWQsCiAgICAgICAgbWFuYWdlcjogdW5kZWZpbmVkLAogICAgICAgIGRvbWFpblVybDogdW5kZWZpbmVkLAogICAgICAgIHN5c3RlbVR5cGU6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZTogdW5kZWZpbmVkLAogICAgICAgIGFzc2V0VHlwZTogdW5kZWZpbmVkLAogICAgICAgIGFzc2V0VHlwZURlc2M6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldENsYXNzOiB1bmRlZmluZWQsCiAgICAgICAgYXNzZXRDbGFzc0Rlc2M6IHVuZGVmaW5lZCwKICAgICAgICBjb25zdHJ1Y3Q6IHVuZGVmaW5lZCwKICAgICAgICBuZXRUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgYXBwVHlwZTogdW5kZWZpbmVkLAogICAgICAgIHNlcnZpY2VHcm91cDogdW5kZWZpbmVkLAogICAgICAgIGZyZXF1ZW5jeTogdW5kZWZpbmVkLAogICAgICAgIHVzYWdlQ291bnQ6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyU2NhbGU6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyT2JqZWN0OiB1bmRlZmluZWQsCiAgICAgICAgdXJsOiB1bmRlZmluZWQsCiAgICAgICAgaXBkOiB1bmRlZmluZWQsCiAgICAgICAgdGVjaG5pY2FsOiB1bmRlZmluZWQsCiAgICAgICAgZGVwbG95OiB1bmRlZmluZWQsCiAgICAgICAgc3RvcmFnZTogdW5kZWZpbmVkLAogICAgICAgIG5ldGVudjogdW5kZWZpbmVkLAogICAgICAgIGlza2V5OiB1bmRlZmluZWQsCiAgICAgICAgZGF0YW51bTogdW5kZWZpbmVkLAogICAgICAgIGlzYmFzZTogIjAiLAogICAgICAgIGlzbGluazogdW5kZWZpbmVkLAogICAgICAgIGlzaGFyZTogdW5kZWZpbmVkLAogICAgICAgIGlzbG9nOiB1bmRlZmluZWQsCiAgICAgICAgaXNwbGFuOiB1bmRlZmluZWQsCiAgICAgICAgaXNhZGFwdDogdW5kZWZpbmVkLAogICAgICAgIGlzY2lwaGVyOiB1bmRlZmluZWQsCiAgICAgICAgYWRhcHREYXRlOiB1bmRlZmluZWQsCiAgICAgICAgY2lwaGVyRGF0ZTogdW5kZWZpbmVkLAogICAgICAgIGZ1bmN0aW9uOiB1bmRlZmluZWQsCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsCiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsCiAgICAgICAgb3JnbklkOiB1bmRlZmluZWQsCiAgICAgICAgdmVuZG9yczogdW5kZWZpbmVkLAogICAgICAgIHVwVGltZTogdW5kZWZpbmVkLAogICAgICAgIGR3aWQ6IHVuZGVmaW5lZCwKICAgICAgICBjb250YWN0b3I6IHVuZGVmaW5lZCwKICAgICAgICBkb21haW5JZDogdW5kZWZpbmVkLAogICAgICAgIG5ldFNjYWxlOiB1bmRlZmluZWQsCiAgICAgICAgbmV0VG9wbzogdW5kZWZpbmVkLAogICAgICAgIG5ldE1lbW86IHVuZGVmaW5lZCwKICAgICAgICB0YWdzOiAiIiwKICAgICAgICBsaW5rczogW10sCiAgICAgICAgZWlkczogW10sCiAgICAgIH07CiAgICAgIHRoaXMuYnVzaW5lc3NGb3JtID0gewogICAgICAgIHN5c0J1c2luZXNzU3RhdGU6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyTnVtczogdW5kZWZpbmVkLAogICAgICAgIGV2ZXJ5ZGF5VmlzaXROdW1zOiB1bmRlZmluZWQsCiAgICAgICAgZXZlcnlkYXlBY3RpdmVOdW1zOiB1bmRlZmluZWQsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJidXNpbmVzc0Zvcm0iKTsKICAgIH0sCgogICAgc2VydmVyU2VsZWN0KGRhdGEpIHsKICAgICAgaWYgKGRhdGEpIHsKICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAnYXNzb2NpYXRpb25TZXJ2ZXInLCBkYXRhLm1hcChpdGVtID0+IGl0ZW0uc2VydmVySWQpKQogICAgICB9CiAgICB9LAoKICAgIC8qKiDmn6Xor6LmiYDlsZ7pg6jpl6ggKi8KICAgIGdldERlcHRUcmVlKCkgewogICAgICBpZiAodGhpcy4kZWRpdGFibGUudmFsdWUpIHsKICAgICAgICBnZXRBbGxEZXB0VHJlZSgpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5kZXB0T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgZGVwdFRyZWVTZWxlY3QoKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAoKICAgIC8vICDmn6Xor6LmiYDmnInotKPku7vkurov55S16K+dCiAgICBnZXRNYW5hZ2VyTGlzdCgpIHsKICAgICAgbGlzdFVzZXIoewogICAgICAgIGlzQXNjOiAnZGVzYycsCiAgICAgICAgb3JkZXJCeUNvbHVtbjogJ2NyZWF0ZVRpbWUnLAogICAgICAgIGlzQWxsRGF0YTogdHJ1ZSwKICAgICAgICB1c2VyTmFtZTogbnVsbCwKICAgICAgICBuaWNrTmFtZTogbnVsbCwKICAgICAgICBwaG9uZW51bWJlcjogbnVsbCwKICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tYW5hZ2VyRGF0YSA9IHJlc3BvbnNlLnJvd3M7CiAgICAgIH0pOwogICAgfSwKCiAgICAvKiog6I635Y+W5Li76YOo572y572R57ucICovCiAgICBnZXROZXR3b3JrRG9tYWluVHJlZSgpIHsKICAgICAgbGlzdERvbWFpbigpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubmV0d29ya0RvbWFpbk9wdGlvbnMgPSByZXNwb25zZS5kYXRhCiAgICAgIH0pOwogICAgfSwKCiAgICAvKiDojrflj5blvIDlj5HlkIjkvZzkvIHkuJogKi8KICAgIGdldFZlbmRvcnNEYXRhKCkgewogICAgICBsaXN0VmVuZG9yQnlBcHBsaWNhdGlvbih7CiAgICAgICAgYXBwbGljYXRpb25JZDogdGhpcy5hc3NldElkLAogICAgICAgIGFwcGxpY2F0aW9uQ29kZTogdGhpcy5mb3JtLnZlbmRvcnMsCiAgICAgICAgaXNBc2M6ICdkZXNjJywKICAgICAgICBvcmRlckJ5Q29sdW1uOiBudWxsLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHZlbmRvckNvZGU6IG51bGwsCiAgICAgICAgdmVuZG9yTmFtZTogbnVsbCwKICAgICAgICB2ZW5kb3JNYW5hZ2VOYW1lOiBudWxsLAogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnZlbmRvcnNEYXRhID0gcmVzcG9uc2Uucm93czsKICAgICAgfSk7CiAgICB9CiAgfSwKfQo="}, {"version": 3, "sources": ["OperationSystemDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs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file": "OperationSystemDetails.vue", "sourceRoot": "src/views/hhlCode/component", "sourcesContent": ["<!--业务系统详情-->\n<template>\n  <div class=\"customForm-container\" style=\"height: 65vh\">\n    <template v-for=\"group in assetList\">\n      <div :key=\"group.formName\" style=\"margin-bottom: 20px;\">\n        <div class=\"my-title\">\n          <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '备案信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '测评信息'\" src=\"@/assets/images/application/cpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '外部连接信息'\" src=\"@/assets/images/application/wblj.png\" alt=\"\">\n          <img v-if=\"group.formName === '拓扑结构信息'\" src=\"@/assets/images/application/tpxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '运营维护情况'\" src=\"@/assets/images/application/ywxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '其他基本信息'\" src=\"@/assets/images/application/qtxx.png\" alt=\"\">\n          <img v-if=\"group.formName === '用户规模'\" src=\"@/assets/images/application/yhgm.png\" alt=\"\">\n          <img v-if=\"group.formName === '业务描述'\" src=\"@/assets/images/application/ywms.png\" alt=\"\">\n          <img v-if=\"group.formName === '功能模块'\" src=\"@/assets/images/application/gnmk.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装服务器环境'\" src=\"@/assets/images/application/fwq.png\" alt=\"\">\n          <img v-if=\"group.formName === '所安装数据库环境'\" src=\"@/assets/images/application/sjk.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联网络设备'\" src=\"@/assets/images/application/wlsb.png\" alt=\"\">\n          <img v-if=\"group.formName === '关联安全设备'\" src=\"@/assets/images/application/aqsb.png\" alt=\"\">\n          {{ group.formName }}\n        </div>\n        <template v-if=\"group.formName === '外部连接信息'\">\n          <ApplicationLink\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            v-model=\"form.links\"/>\n        </template>\n        <template v-else-if=\"group.formName === '运营维护情况'\">\n          <ApplicationSite\n            ref=\"site\"\n            :fields=\"group.fieldsItems\"\n            :disabled=\"!$editable.value\"\n            :value.sync=\"form.eids\"\n            :asset-id=\"form.assetId\"\n            multiple/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装服务器环境'\">\n          <serverEV\n            class=\"my-form\"\n            ref=\"serverEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"\n            :data-list=\"currentAssociationServer\"\n            @selected=\"serverSelect\"\n            v-if=\"afterInit\"/>\n        </template>\n        <template v-else-if=\"group.formName === '所安装数据库环境'\">\n          <dateEV\n            class=\"my-form\"\n            ref=\"dateEV\"\n            :fields=\"group.fieldsItems\"\n            :function-list.sync=\"functionStateList\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联网络设备'\">\n          <network-e-v\n            class=\"my-form\"\n            ref=\"networkEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '关联安全设备'\">\n          <safeEV\n            class=\"my-form\"\n            ref=\"safeEV\"\n            :fields=\"group.fieldsItems\"\n            :asset-id=\"assetId\"/>\n        </template>\n        <template v-else-if=\"group.formName === '功能模块'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item :span=\"3\" v-for=\"(item, index) in functionStateList\" :key=\"index\">\n              <div style=\"display: flex; justify-content: space-around\">\n                <div>{{ item.moduleName }}</div>\n                <div>{{ item.moduleDesc }}</div>\n              </div>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '用户规模'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === businessForm[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n              <template v-else-if=\"field.fieldKey === 'serviceGroup'\">\n                <div class=\"tag-group\">\n                  <template v-if=\"processedServiceGroups.length > 0\">\n                    <span v-for=\"(label, index) in processedServiceGroups\" :key=\"index\">\n                      {{ label }}\n                    </span>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择</span>\n                </div>\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else-if=\"group.formName === '业务描述'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"businessForm[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n              <template v-else>\n                <span>{{ businessForm[field.fieldKey] }}</span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n        <template v-else>\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              v-if=\"shouldShowField(field)\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\"\n              :span=\"getFieldSpan(field)\">\n\n              <!-- 上传类型字段 -->\n              <template v-if=\"uploadType.includes(field.fieldKey)\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"form[field.fieldKey]\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </template>\n\n              <!-- 特殊字段：关联服务器 -->\n              <template v-else-if=\"field.fieldKey === 'associationServer'\">\n                <div class=\"server-display\">\n                  <div v-for=\"id in form.associationServer\" :key=\"id\" class=\"server-item\">\n                    <span>{{ getServerName(id) }}</span>\n                  </div>\n                </div>\n              </template>\n\n              <!-- 特殊字段：责任人 -->\n              <template v-else-if=\"field.fieldKey === 'manager'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedManagerList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedManagerList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}（{{ user.phone }}）\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择责任人</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：单位 -->\n              <template v-else-if=\"field.fieldKey === 'deptId'\">\n                <span\n                  v-for=\"(item, index) in deptOptions\"\n                  :key=\"item.id\"\n                  v-if=\"item.id === form.deptId\"\n                >{{ item.label }}</span>\n              </template>\n\n              <!-- 特殊字段：主部署网络 -->\n              <template v-else-if=\"field.fieldKey === 'domainId'\">\n                <span\n                  v-for=\"(item, index) in networkDomainOptions\"\n                  :key=\"item.domainId\"\n                  v-if=\"item.domainId === form.domainId\"\n                >{{ item.domainName }}</span>\n              </template>\n\n              <!-- 特殊字段：开发合作企业 -->\n              <template v-else-if=\"field.fieldKey === 'vendor'\">\n                <div class=\"manager-tags\">\n                  <template v-if=\"processedVendorsList.length > 0\">\n                    <el-tag\n                      v-for=\"user in processedVendorsList\"\n                      :key=\"user.id\"\n                      size=\"small\"\n                    >\n                      {{ user.name }}\n                    </el-tag>\n                  </template>\n                  <span v-else class=\"gray-text\">未选择开发合作企业</span>\n                </div>\n              </template>\n\n              <!-- 特殊字段：标签 -->\n              <template v-else-if=\"field.fieldKey === 'tags'\">\n                <template v-if=\"(form.tags || '').split(',').filter(t => t).length > 0\">\n                  <el-tag\n                    v-for=\"(tag,index) in (form.tags || '').split(',')\"\n                    :key=\"index\"\n                    closable\n                    size=\"small\"\n                    v-show=\"tag\"\n                  >\n                    {{ tag }}\n                  </el-tag>\n                </template>\n                <span v-else class=\"gray-text\">暂无标签</span>\n              </template>\n\n              <!-- 下拉选择类型字段 -->\n              <template v-else-if=\"selectType.includes(field.fieldKey)\">\n                <span\n                  v-for=\"item in getDictOptions(field.fieldKey)\"\n                  v-show=\"item.value === form[field.fieldKey]\"\n                  :key=\"item.value\">\n                  {{ item.label }}\n                </span>\n              </template>\n\n              <!-- 默认文本显示 -->\n              <template v-else>\n                <span>{{ getFieldValue(field) }}</span>\n              </template>\n            </el-descriptions-item>\n\n          </el-descriptions>\n        </template>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport {getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {getValFromObject} from \"@/utils\";\nimport {generateSecureUUID, waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\nimport serverEV from \"@/views/hhlCode/component/application/applicationHardware/serverEV.vue\";\nimport dateEV from \"@/views/hhlCode/component/application/applicationHardware/dateEV.vue\";\nimport networkEV from \"@/views/hhlCode/component/application/applicationHardware/networkEV.vue\";\nimport safeEV from \"@/views/hhlCode/component/application/applicationHardware/safeEV.vue\";\nimport overViewSelect from \"@/views/components/select/overViewSelect.vue\";\nimport {listAllOverview} from \"@/api/safe/overview\";\nimport EditServer from \"@/views/safe/server/editServer.vue\";\nimport {getAllDeptTree, deptTreeSelect, listUser} from \"@/api/system/user\"\nimport {listDomain} from \"@/api/dict/domain\";\n\nexport default {\n  name: \"OperationSystemDetails\",\n  components: {\n    EditServer,\n    overViewSelect,\n    safeEV,\n    networkEV,\n    dateEV,\n    serverEV,\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'serve_group',\n    'cover_area',\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status',\n    'is_open_network',\n    'hw_is_true_shut_down'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    assetList: {\n      type: Array,\n      default: () => []\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      functionStateList: [{}, {}, {}],\n      // 基本信息表单参数\n      form: {},\n      // 业务信息表单参数\n      businessForm: {\n        delList: []\n      },\n      deptOptions: [],\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人',\n      managerData: [],\n      networkDomainOptions: [],\n      vendorsData: [],\n      refs: {\n        'networkEV': \"所安装服务器环境\",\n        'safeEV': '所安装数据环境',\n        'serverEV': '关联网络设备',\n        'dateEV': \"关联安全设备\"\n      },\n      collapse: ['1', '2', '3', '4'],\n      showAddServer: false,\n      serverOptions: [],\n      currentAssociationServer: [],\n      afterInit: false,\n      uploadType: ['waitingInsuranceFilingScan', 'evaluationReport', 'netTopo', 'operateHandbook'],\n      selectType: ['systemType', 'construct', 'loginType', 'technical', 'deploy', 'state', 'protectGrade', 'evaluationResults', 'evaluationStatus'],\n    }\n  },\n  mounted() {\n    this.getAllServerList();\n    this.getDeptTree();\n    this.getManagerList();\n    this.getNetworkDomainTree();\n    this.getVendorsData();\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  computed: {\n    // 获取服务器名称映射\n    getServerName() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.assetName || ''\n    },\n    // 获取服务器IP映射\n    getServerIp() {\n      return (id) => this.serverOptions.find(s => s.assetId === id)?.ip || ''\n    },\n    processedManagerList() {\n      // 去重\n      const ids = [...new Set(\n        (this.form.manager || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.managerData.find(u =>\n          Number(u.userId) === Number(id)\n        );\n        return {\n          id,\n          name: user?.nickName || '未知用户',\n          phone: user?.phonenumber || ''\n        };\n      });\n    },\n    processedVendorsList() {\n      const ids = [...new Set(\n        (this.form.vendors || '')\n          .split(',')\n          .filter(Boolean)\n      )];\n\n      return ids.map(id => {\n        const user = this.vendorsData.find(u =>\n          Number(u.id) === Number(id)\n        );\n        return {\n          id,\n          name: user?.vendorName || '未知用户',\n        };\n      });\n    },\n    processedServiceGroups() {\n      if (!this.businessForm.serviceGroup) return []\n      return this.businessForm.serviceGroup.split(',')\n        .map(val => this.dict.type['serve_group'].find(d => d.value === val)?.label || val)\n    }\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n  watch: {\n    functionStateList: {\n      handler(newVal, oldVal) {\n        if (newVal && newVal.length > 0) {\n          newVal.forEach((item, index) => {\n            if (Object.keys(item).length > 0) {\n              item.tempId = generateSecureUUID();\n            }\n          })\n        }\n      },\n    },\n    'form.systemType': {\n      handler(newVal, oldVal) {\n        if (newVal) {\n          this.form.systemType = newVal.toString();\n        }\n      }\n    },\n  },\n  methods: {\n    getFieldValue(field) {\n      // 其他基本信息字段格式化\n      let filterArr = ['isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'iskey', 'isOpenNetwork']\n      if (filterArr.includes(field.fieldKey)) {\n        return this.form[field.fieldKey] === 'Y' ? '是' : '否';\n      }\n      if(field.fieldKey === 'hwIsTrueShutDown'){\n        return this.dict.type.hw_is_true_shut_down.find(d => d.value === this.form[field.fieldKey])?.label || this.form[field.fieldKey];\n      }\n      return this.form[field.fieldKey];\n    },\n\n    getFieldSpan(field) {\n      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan'];\n      if (fullSpanFields.includes(field.fieldKey)) return 3;\n      // 其他字段默认占8列\n      return 1;\n    },\n    // 判断字段是否显示\n    shouldShowField(field) {\n      if (field.fieldKey === 'otherSystemNotes') {\n        return this.form.systemType === '12';\n      }\n      if (field.fieldKey === 'adaptDate') {\n        return this.form.isadapt === 'Y';\n      }\n      if (field.fieldKey === 'cipherDate') {\n        return this.form.iscipher === 'Y';\n      }\n      if (field.fieldKey === 'islink') {\n        return this.deployLocation === 'fair';\n      }\n      return true;\n    },\n\n    getDictOptions(fieldKey) {\n      const dictMap = {\n        systemType: 'system_type',\n        construct: 'construct_type',\n        loginType: 'app_login_type',\n        technical: 'app_technical',\n        deploy: 'app_deploy',\n        state: 'asset_state',\n        protectGrade: 'protection_grade',\n        evaluationResults: 'evaluation_results',\n        evaluationStatus: 'evaluation_status',\n        hwIsTrueShutDown: 'hw_is_true_shut_down'\n      };\n      return this.dict.type[dictMap[fieldKey]] || [];\n    },\n\n    getAllServerList() {\n      listAllOverview({\"assetClass\": 4}).then(res => {\n        this.serverOptions = res.data;\n      })\n    },\n    /** 初始化 */\n    async init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        await getApplication(this.assetId).then(response => {\n          // 获取应用信息详情\n          this.form.assetId = this.assetId;\n          this.form = response.data.applicationVO;\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n          // 获取业务信息详情\n          this.businessForm.assetId = this.assetId;\n          this.businessForm = response.data.tblBusinessApplication;\n          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';\n          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';\n          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';\n          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];\n          if (this.functionStateList.length < 3) {\n            let i = 0;\n            while (i < 3 - this.functionStateList.length) {\n              this.functionStateList.push({});\n            }\n          }\n        }).finally(() => {\n          this.afterInit = true;\n        })\n      } else {\n        this.afterInit = true;\n      }\n    },\n\n\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.businessForm = {\n        sysBusinessState: undefined,\n        userNums: undefined,\n        everydayVisitNums: undefined,\n        everydayActiveNums: undefined,\n      };\n      this.resetForm(\"form\");\n      this.resetForm(\"businessForm\");\n    },\n\n    serverSelect(data) {\n      if (data) {\n        this.$set(this.form, 'associationServer', data.map(item => item.serverId))\n      }\n    },\n\n    /** 查询所属部门 */\n    getDeptTree() {\n      if (this.$editable.value) {\n        getAllDeptTree().then(response => {\n          this.deptOptions = response.data;\n        });\n      } else {\n        deptTreeSelect().then(response => {\n          this.deptOptions = response.data;\n        });\n      }\n    },\n\n    //  查询所有责任人/电话\n    getManagerList() {\n      listUser({\n        isAsc: 'desc',\n        orderByColumn: 'createTime',\n        isAllData: true,\n        userName: null,\n        nickName: null,\n        phonenumber: null,\n      }).then(response => {\n        this.managerData = response.rows;\n      });\n    },\n\n    /** 获取主部署网络 */\n    getNetworkDomainTree() {\n      listDomain().then(response => {\n        this.networkDomainOptions = response.data\n      });\n    },\n\n    /* 获取开发合作企业 */\n    getVendorsData() {\n      listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        isAsc: 'desc',\n        orderByColumn: null,\n        pageNum: 1,\n        pageSize: 10,\n        vendorCode: null,\n        vendorName: null,\n        vendorManageName: null,\n      }).then(response => {\n        this.vendorsData = response.rows;\n      });\n    }\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n\n.server-display {\n  line-height: 1.8;\n  display: flex;\n}\n\n.server-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 0 5px;\n}\n</style>\n"]}]}
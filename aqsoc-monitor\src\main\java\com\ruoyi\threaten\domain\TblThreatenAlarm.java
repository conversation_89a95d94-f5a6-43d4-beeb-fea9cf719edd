package com.ruoyi.threaten.domain;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.safe.domain.TblBusinessApplication;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.annotations.Options;

import java.util.Date;
import java.util.List;

/**
 * 威胁情报对象 tbl_threaten_alarm
 *
 * <AUTHOR>
 * @date 2023-11-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TblThreatenAlarm extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /* 无工单状态 */
    public static final String NO_WORK="99";
    /* 已完成工单状态 */
    public static final String IS_FINISH_WORK="4";

    public static final Long NO_HIT = 0l;
    public static final Long HIT = 1l;

    public static final int SCAN_DATA = 1;     // 扫描
    public static final int MANUAL_DATA = 2;   // 手动
    public static final int HONEYPOT_DATA = 7; // 蜜罐
    public static final int FLOW_DATA = 8;     // 流量
    public static final int PROBE_DATA = 9;    // 探针

    public static final int UNKNOW_HAZARD = 0;
    public static final int NO_HAZARD = 1;
    public static final int LOW_HAZARD =2;
    public static final int MID_HAZARD =3;
    public static final int HIGH_HAZARD =4;
    public static final int FATAL_HAZARD=5;

    public static final String INVESTIGATE = "0";
    public static final String INTRUSION = "1";
    public static final String LOAD_DELIVERY = "2";
    public static final String CMD_CONTROL = "3";
    public static final String INNER_DIFFUSE = "4";
    public static final String FINISH = "5";

    public static final String ATTACK_RESLUT_UNKONW = "0";
    public static final String ATTACK_RESULT_SUCC = "1";
    public static final String ATTACK_RESULT_FAIL = "2";
    public static final String ATTACK_RESULT_MAYBE = "3";

    /** 自增id */
    private Long id;

    private List<Long> ids;

    private List<Long> notIds;

    /** 工单 */
    private Long workId;

    /** 工单任务ID */
    private String prodId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "*告警时间",dateFormat ="yyyy-MM-dd HH:mm:ss" )
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "*告警更新时间",dateFormat ="yyyy-MM-dd HH:mm:ss" )
    private Date updateTime;

    /** 告警名称 */
    @Excel(name = "*告警名称")
    private String threatenName;

    private String srcThreatenName;

    /** 告警类型 */
    @Excel(name = "*告警类型")
    private String threatenType;

    private Integer threatenId;

    /** 产生原因 */
    @Excel(name = "*告警原因")
    private String reason;

    /** 标签 */
    @Excel(name = "威胁标签")
    private String label;

    /** 失陷状态 */
    private String lossState;

    /** 处理建议 */
    @Excel(name = "*处置建议")
    private String handSuggest;

    /** 告警等级： 0：未知  1：无威胁 2： 低危  3： 中危  4： 高危 5： 严重 */
    @Excel(name = "*告警等级",readConverterExp = "1=无威胁,2=低危,3=中危,4=高危,5=严重",combo = "无威胁,低危,中危,高危,严重")
    private Integer alarmLevel;

    /** 源ip */
    @Excel(name = "*攻击IP")
    private String srcIp;

    /** 源端口 */
    @Excel(name = "*攻击IP端口")
    private Integer srcPort;

    /** 协议 */
    @Excel(name = "协议")
    private String procotol;

    /** 目标ip */
    @Excel(name = "*受害IP/应用")
    private String destIp;

    private List<String> destIps;

    /** 目标端口 */
    @Excel(name = "*受害端口")
    private Integer destPort;

    @Excel(name = "关联设备")
    private String associaDevice;

    //    @Excel(name = "处置状态",readConverterExp = "0=IP封禁,1=白名单,2=忽略,99=未处置")
    private String handleState;
    private String handleDesc;

    @Excel(name = "工单状态",readConverterExp = "0=待审核,1=待处置,2=待验证,3=已完成,99=待分配")
    private String flowState;

    /** 数据来源 */
    @Excel(name = "数据来源",readConverterExp = "1=探测,2=手动")
    private Integer dataSource;

    /** 告警次数 **/
    @Excel(name="发现次数")
    private Integer alarmNum;

    /** 是否命中情报：（空：未知  0：未命中情报 1：命中情报） */
    private Long hitIntelligence;

    /*处理类型*/
    private String handleType;

    /*上传文件*/
    private String fileUrl;

    /*资产名称*/
    private String assetName;
    private Long assetType;
    /*资产分类描述*/
    private String assetTypeDesc;
    /*资产类型描述*/
    private String assetClassDesc;
    /*资产类id*/
    private Long assetId;

    private List<TblBusinessApplication> businessApplications;

    private String domainAlarm;
    private Date startTime;
    private Date endTime;
    // 限制时间范围
    private Date limitStartTime;
    private Date limitEndTime;

    private Date startCreateTime;

    private String strategy;

    /** 是否查询 top10 */
    private boolean top10 = false;

    /** 需要展示的危险程度 */
    private List<Integer> showAlarmLevels;
    /**
     * 部门id
     */
    private Long deptId;

    private String deptIdStr;

    /**
     * 部门名称
     */
    private String deptName;

    // 攻击ip
    private String attackIp;
    // 攻击port
    private Integer attackPort;
    // 受害ip
    private String victimIp;
    //受害port
    private Integer victimPort;
    // 是否资产
    private String isAsset;
    // 资产ip  /// 时间线 是否资产判断 资产ip
    private String ipv4;
    private String srcIpv4;
    private String destIpv4;
    // 时间线 是否资产判断 资产ip
    private String isIpv4;

    // 查询条件  查询源IP or 目标IP
    private String ipOr;


    /**
     * 阶段名称
     */
    private String attackSeg;

    private List<String> threatenTypeList;

    private Long applicationId;

    /** 1=高风险 */
    private Integer queryAlarmLevel;

    private String domainId;

    private TblThreatenTypeSeg typeSeg;

    private String typeSegStr;

    private String dataId;

    /**
     * 时间范围:
     *  1:一天
     * 	2:一周
     * 	3:一月
     * 	4:三月
     * 	5:半年
     */
    private String timeRange;

    private List<String> ipv4List;

    private Long workOrderId;

    private List<Long> workOrderIdList;

    private String disposer; // 处置人
    private String synchronizationStatus; // 同步状态
    private List<Long> disposers;


    private Boolean isBlocking = false; //是否阻断中

    private String cycleType; //周期类型 1=本周 2=本月 3=本季 4=本年

    public String getKey() {
        return threatenName + "_" + threatenType + "_" + srcIp + "_" + destIp + "_" + destPort + "_" + dataSource;
    }

    public void checkImportData(int i) throws Exception {
        if ((threatenName == null) || (threatenName.trim().equals(""))) {
            throw new ServiceException("第"+(i+1)+"行告警名称为空!");
        }

        if ((threatenType == null) || (threatenType.trim().equals(""))) {
            throw new ServiceException("第"+(i+1)+"行告警类型为空!");
        }

        if ((reason == null) || (reason.trim().equals(""))) {
            throw new ServiceException("第"+(i+1)+"行告警原因为空!");
        }

        if ((handSuggest == null) || (handSuggest.trim().equals(""))) {
            throw new ServiceException("第"+(i+1)+"行处置建议为空!");
        }

        if ((srcIp == null) || (srcIp.trim().equals(""))) {
            throw new ServiceException("第"+(i+1)+"行攻击IP为空!");
        }
        if (!srcIp.matches("^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$")) {
            throw new ServiceException("第"+(i+1)+"行"+"【"+srcIp+"】"+"攻击IP格式有误!");
        }

        if (srcPort == null) {
            throw new ServiceException("第"+(i+1)+"行攻击IP端口为空!");
        }
        if (!srcPort.toString().matches( "^([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-4]\\d{4}|65[0-4]\\d{2}|655[0-2]\\d|6553[0-5])$")){
            throw new ServiceException("第"+(i+1)+"行攻击端口格式有误");
        }

        if ((destIp == null) || (destIp.trim().equals(""))) {
            throw new Exception("第"+(i+1)+"行受害IP/应用为空!");
        }
        if (!destIp.matches("^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$")) {
            throw new ServiceException("第"+(i+1)+"行"+"【"+destIp+"】"+"受害IP/应用格式有误!");
        }

        if (destPort == null) {
            throw new ServiceException("第"+(i+1)+"行受害端口为空!");
        }
        if (!destPort.toString().matches( "^([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-4]\\d{4}|65[0-4]\\d{2}|655[0-2]\\d|6553[0-5])$")){
            throw new ServiceException("第"+(i+1)+"行受害端口格式有误");
        }


        if (createTime == null)  {
            throw new ServiceException("第"+(i+1)+"行告警时间为空!");
        }

        if (updateTime == null)  {
            throw new ServiceException("第"+(i+1)+"行告警更新时间为空!");
        }

        if (alarmLevel == null)  {
            throw new ServiceException("第"+(i+1)+"行告警等级为空!");
        }
    }
}


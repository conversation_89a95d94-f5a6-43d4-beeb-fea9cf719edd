{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\boundary\\BoundaryDetailDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\boundary\\BoundaryDetailDialog.vue", "mtime": 1754969114219}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_assetOverViewTable", "_interopRequireDefault", "require", "_index", "_user", "name", "components", "AssetFile", "assetOverViewTable", "props", "boundaryDetailVisible", "type", "Boolean", "default", "title", "String", "form", "Object", "editable", "editItem", "children", "Array", "queryParamsBoundaryAsset", "dictOptions", "getBoundaryAssetsList", "Function", "handleAddAsset", "handleDeleteAsset", "assetList", "data", "dialogVisible", "visible", "deptOptions", "computed", "get", "set", "val", "$emit", "visibleAssetFields", "filter", "item", "isShow", "dictFields", "created", "getDeptTree", "methods", "getDictData", "<PERSON><PERSON><PERSON>", "_this", "getAllDeptTree", "then", "response", "getFieldValue", "field", "includes", "assetTypeDesc"], "sources": ["src/views/safe/boundary/BoundaryDetailDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog :visible.sync=\"visible\" :title=\"title\" width=\"50%\">\n    <div class=\"customForm-container\" style=\"padding: 0 20px\">\n      <div class=\"my-title\"><img src=\"@/assets/images/application/jbxx.png\" alt=\"\">基本信息</div>\n      <el-descriptions\n        class=\"custom-column\"\n        direction=\"vertical\"\n        size=\"medium\"\n        :colon=\"false\"\n        label-class-name=\"custom-label-style\"\n        content-class-name=\"custom-content-style\"\n        :column=\"2\"\n      >\n        <template v-for=\"item in visibleAssetFields\">\n          <el-descriptions-item :key=\"item.fieldKey\" :label=\"item.fieldName\">\n            <!-- 字典字段特殊处理 -->\n            <template v-if=\"dictFields.includes(item.fieldKey)\">\n              <span\n                v-for=\"dict in getDictData(item.fieldKey)\"\n                :key=\"dict.value\"\n                v-if=\"dict.value === form[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <template v-if=\"item.fieldKey === 'deptId'\">\n              <span\n                v-for=\"dict in deptOptions\"\n                :key=\"dict.id\"\n                v-if=\"dict.id === form[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <!-- 普通字段直接显示 -->\n            <template v-else>\n              {{ getFieldValue(item.fieldKey) }}\n            </template>\n          </el-descriptions-item>\n        </template>\n      </el-descriptions>\n\n      <div class=\"my-title\" style=\"margin-top: 20px;\">\n        <i class=\"el-icon-office-building icon\" />\n        区域内资产\n      </div>\n      <asset-over-view-table\n        style=\"width: 100%;\n              padding-right: 10px;\"\n        :disabled=\"!editable\"\n        :if-reflush=\"dialogVisible\"\n        :asset-overview-query=\"queryParamsBoundaryAsset\"\n        :asset-class=\"[2, 3, 5, 9]\"\n        :asset-find-fun=\"getBoundaryAssetsList\"\n        :asset-add-fun=\"handleAddAsset\"\n        :asset-delete-fun=\"handleDeleteAsset\"\n      />\n\n      <div class=\"my-title\" style=\"margin-top: 20px;\">\n        <i class=\"el-icon-files icon\" />\n        相关文件\n      </div>\n      <asset-file style=\"width: 100%; padding-right: 10px;\"\n                  :disabled=\"!editable\"\n                  :asset-id=\"form.assetId\"\n      />\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport assetOverViewTable from \"@/views/components/table/assetOverViewTable.vue\";\nimport AssetFile from \"@/views/dimension/file/index.vue\";\nimport {deptTreeSelect, getAllDeptTree} from \"@/api/system/user\";\n\nexport default {\n  name: \"BoundaryDetailDialog\",\n  components: {AssetFile, assetOverViewTable},\n  props: {\n    boundaryDetailVisible: {\n      type: Boolean,\n      default: false\n    },\n    title: {\n      type: String,\n      default: '查看边界区域'\n    },\n    form: Object,\n    editable: Boolean,\n    editItem: String,\n    children: Array,\n    queryParamsBoundaryAsset: Object,\n    dictOptions: Array,\n    getBoundaryAssetsList: Function,\n    handleAddAsset: Function,\n    handleDeleteAsset: Function,\n    assetList: {\n      type: Array,\n      default: function () {\n        return []\n      }\n    }\n  },\n  data() {\n    return {\n      dialogVisible: this.visible,\n      deptOptions: []\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.boundaryDetailVisible;\n      },\n      set(val) {\n        this.$emit('update:boundaryDetailVisible', val);\n      }\n    },\n\n    visibleAssetFields() {\n      return this.assetList.filter(item => item.isShow);\n    },\n\n    dictFields() {\n      return ['degreeImportance'];\n    }\n  },\n  created() {\n    this.getDeptTree();\n  },\n  methods: {\n    getDictData(fieldKey) {\n      switch (fieldKey) {\n        case 'degreeImportance':\n          return this.dictOptions;\n        default:\n          return [];\n      }\n    },\n    /** 查询所属部门 */\n    getDeptTree() {\n      getAllDeptTree().then(response => {\n        this.deptOptions = response.data;\n      });\n    },\n    getFieldValue(field) {\n      if(this.dictFields.includes(field)){\n        return '';\n      }\n      if (field === 'assetType') {\n        return this.form.assetTypeDesc || this.form[field];\n      }\n      return this.form[field];\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/customForm\";\n::v-deep .el-dialog__body {\n  padding: 0 0 20px 0 !important;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AAyEA,IAAAA,mBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAG,IAAA;EACAC,UAAA;IAAAC,SAAA,EAAAA,cAAA;IAAAC,kBAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,qBAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,IAAA,EAAAC,MAAA;IACAC,QAAA,EAAAN,OAAA;IACAO,QAAA,EAAAJ,MAAA;IACAK,QAAA,EAAAC,KAAA;IACAC,wBAAA,EAAAL,MAAA;IACAM,WAAA,EAAAF,KAAA;IACAG,qBAAA,EAAAC,QAAA;IACAC,cAAA,EAAAD,QAAA;IACAE,iBAAA,EAAAF,QAAA;IACAG,SAAA;MACAjB,IAAA,EAAAU,KAAA;MACAR,OAAA,WAAAA,SAAA;QACA;MACA;IACA;EACA;EACAgB,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA,OAAAC,OAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACAF,OAAA;MACAG,GAAA,WAAAA,IAAA;QACA,YAAAxB,qBAAA;MACA;MACAyB,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,iCAAAD,GAAA;MACA;IACA;IAEAE,kBAAA,WAAAA,mBAAA;MACA,YAAAV,SAAA,CAAAW,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;IACA;IAEAC,UAAA,WAAAA,WAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,QAAA;MACA,QAAAA,QAAA;QACA;UACA,YAAAxB,WAAA;QACA;UACA;MACA;IACA;IACA,aACAqB,WAAA,WAAAA,YAAA;MAAA,IAAAI,KAAA;MACA,IAAAC,oBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAhB,WAAA,GAAAmB,QAAA,CAAAtB,IAAA;MACA;IACA;IACAuB,aAAA,WAAAA,cAAAC,KAAA;MACA,SAAAX,UAAA,CAAAY,QAAA,CAAAD,KAAA;QACA;MACA;MACA,IAAAA,KAAA;QACA,YAAArC,IAAA,CAAAuC,aAAA,SAAAvC,IAAA,CAAAqC,KAAA;MACA;MACA,YAAArC,IAAA,CAAAqC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}
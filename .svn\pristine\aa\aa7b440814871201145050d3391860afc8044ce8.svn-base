<!-- 网络、安全设备详情 -->
<template>
  <el-dialog :visible.sync="visible" :title="assetName" width="50%">
    <div class="customForm-container" style="padding: 0 20px">
      <div class="my-title"><img src="@/assets/images/application/jbxx.png" alt="">基本信息</div>
      <el-descriptions
        class="custom-column"
        direction="vertical"
        size="medium"
        :colon="false"
        label-class-name="custom-label-style"
        content-class-name="custom-content-style"
        :column="3"
      >
        <template v-for="item in visibleAssetFields">
          <el-descriptions-item :key="item.fieldKey" :label="item.fieldName">
            <!-- 字典字段特殊处理 -->
            <template v-if="dictFields.includes(item.fieldKey)">
              <span
                v-for="dict in getDictData(item.fieldKey)"
                :key="dict.value"
                v-if="dict.value === asset[item.fieldKey]"
              >
                {{ dict.label }}
              </span>
            </template>

            <template v-if="['deptId', 'manageDeptId'].includes(item.fieldKey)">
              <span
                v-for="dict in deptOptions"
                :key="dict.id"
                v-if="dict.id === asset[item.fieldKey]"
              >
                {{ dict.label }}
              </span>
            </template>

            <!-- 普通字段直接显示 -->
            <template v-else>
              {{ getFieldValue(item.fieldKey) }}
            </template>
          </el-descriptions-item>
        </template>
      </el-descriptions>

      <div class="my-title" style="margin: 20px 0"><img src="@/assets/images/application/netinfo.png" alt="">网络信息
      </div>
      <el-table v-loading="loadingIpMac" :data="ipMacList">
        <el-table-column label="所属网络" prop="domainFullName" show-overflow-tooltip/>
        <el-table-column label="ipv4/ipv6" prop="ipv4"/>
        <el-table-column label="MAC" prop="mac"/>
        <el-table-column label="网关" prop="defaultGateway"/>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip/>
      </el-table>
      <pagination
        v-show="totalIpMac>=0"
        :total="totalIpMac"
        :page.sync="queryParamsIpMac.pageNum"
        :limit.sync="queryParamsIpMac.pageSize"
        @pagination="getIpMacList"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {getAssetInfo} from "@/api/safe/overview";
import {listMacAip} from "@/api/safe/macAip";
import assetRegister from "@/mixins/assetRegister";
import {getAllDeptTree} from "@/api/system/user";

export default {
  mixins: [assetRegister],
  name: "networkDeviceDetail",
  props: {
    assetId: {
      type: String,
      default: null
    },
    assetName: {
      type: String,
      default: null
    },
    deviceDetailVisible: {
      type: Boolean,
      default: false
    },
    assetAllocationType: {
      type: String,
      default: '4'
    }
  },
  dicts: ['sys_yes_no', 'impt_grade', 'is_sparing'], // 保留字典配置
  data() {
    return {
      asset: {},
      queryParamsIpMac: {
        pageNum: 1,
        pageSize: 10,
        assetId: null,
      },
      ipMacList: [],
      totalIpMac: 0,
      loadingIpMac: false,
      deptOptions: [],
    };
  },
  watch: {
    assetId(newVal) {
      if (newVal) {
        this.getAsset();
        this.getIpMacList();
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.deviceDetailVisible;
      },
      set(val) {
        this.$emit("update:deviceDetailVisible", val);
      }
    },
    visibleAssetFields() {
      // 获取资产字段列表
      const assetListData = this.assetList && this.assetList.length ? this.assetList[0].fieldsItems : []
      return assetListData.filter(item => item.isShow);
    },

    // 需要字典转换的字段
    dictFields() {
      return ['isVirtual', 'degreeImportance', 'isSparing'];
    }
  },
  created() {
    this.getDeptTree();
  },
  methods: {
    getDictData(fieldKey) {
      switch (fieldKey) {
        case 'isVirtual':
          return this.dict.type.sys_yes_no;
        case 'degreeImportance':
          return this.dict.type.impt_grade;
          case 'isSparing':
            return this.dict.type.is_sparing;
        default:
          return [];
      }
    },

    /** 查询所属部门 */
    getDeptTree() {
      getAllDeptTree().then(response => {
        // 将部门树结构转换为平级结构
        const flattenDeptTree = (nodes) => {
          let result = [];
          nodes.forEach(node => {
            result.push({
              id: node.id,
              label: node.label
            });
            if (node.children && node.children.length > 0) {
              result = result.concat(flattenDeptTree(node.children));
            }
          });
          return result;
        };

        this.deptOptions = flattenDeptTree(response.data);
      });
    },

    getAsset() {
      getAssetInfo(this.assetId).then(response => {
        this.asset = response.data || {};
      });
    },
    getIpMacList() {
      this.loadingIpMac = true;
      this.queryParamsIpMac.assetId = this.assetId;
      listMacAip(this.queryParamsIpMac).then(response => {
        this.ipMacList = response.rows;
        this.totalIpMac = response.total;
        this.loadingIpMac = false;
      });
    },
    getFieldValue(field) {
      if(this.dictFields.includes(field)){
        return '';
      }
      const value = this.asset[field];
      if (field === 'maintainUnit') {
        return this.asset.maintainUnitName;
      }
      if (field === 'facilityManufacturer') {
        return this.asset.facilityManufacturerName || value;
      }
      if (field === 'vendor') {
        return this.asset.vendorName || value;
      }
      if (field === 'assetType') {
        return this.asset.assetTypeDesc || value;
      }
      if (field === 'domainId') {
        return this.asset.domainName || value;
      }
      if (field === 'locationId') {
        return this.asset.locationFullName || value;
      }
      return value;
    },
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/customForm";
::v-deep .el-dialog__body {
  padding: 0 0 20px 0 !important;
}
</style>

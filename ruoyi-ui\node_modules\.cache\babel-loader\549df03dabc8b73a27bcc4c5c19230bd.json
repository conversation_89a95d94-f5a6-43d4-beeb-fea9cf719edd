{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\networkdevices\\networkDeviceDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\networkdevices\\networkDeviceDetail.vue", "mtime": 1754969114156}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi93c2gvYXVnbWVudF93b3Jrc3BhY2UvYXFzb2MtbWFpbi9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyIpOwp2YXIgX292ZXJ2aWV3ID0gcmVxdWlyZSgiQC9hcGkvc2FmZS9vdmVydmlldyIpOwp2YXIgX21hY0FpcCA9IHJlcXVpcmUoIkAvYXBpL3NhZmUvbWFjQWlwIik7CnZhciBfYXNzZXRSZWdpc3RlciA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9taXhpbnMvYXNzZXRSZWdpc3RlciIpKTsKdmFyIF91c2VyID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL3VzZXIiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG1peGluczogW19hc3NldFJlZ2lzdGVyLmRlZmF1bHRdLAogIG5hbWU6ICJuZXR3b3JrRGV2aWNlRGV0YWlsIiwKICBwcm9wczogewogICAgYXNzZXRJZDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0sCiAgICBhc3NldE5hbWU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9LAogICAgZGV2aWNlRGV0YWlsVmlzaWJsZTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIGFzc2V0QWxsb2NhdGlvblR5cGU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnNCcKICAgIH0KICB9LAogIGRpY3RzOiBbJ3N5c195ZXNfbm8nLCAnaW1wdF9ncmFkZScsICdpc19zcGFyaW5nJ10sCiAgLy8g5L+d55WZ5a2X5YW46YWN572uCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFzc2V0OiB7fSwKICAgICAgcXVlcnlQYXJhbXNJcE1hYzogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGFzc2V0SWQ6IG51bGwKICAgICAgfSwKICAgICAgaXBNYWNMaXN0OiBbXSwKICAgICAgdG90YWxJcE1hYzogMCwKICAgICAgbG9hZGluZ0lwTWFjOiBmYWxzZSwKICAgICAgZGVwdE9wdGlvbnM6IFtdCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIGFzc2V0SWQ6IGZ1bmN0aW9uIGFzc2V0SWQobmV3VmFsKSB7CiAgICAgIGlmIChuZXdWYWwpIHsKICAgICAgICB0aGlzLmdldEFzc2V0KCk7CiAgICAgICAgdGhpcy5nZXRJcE1hY0xpc3QoKTsKICAgICAgfQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHZpc2libGU6IHsKICAgICAgZ2V0OiBmdW5jdGlvbiBnZXQoKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZGV2aWNlRGV0YWlsVmlzaWJsZTsKICAgICAgfSwKICAgICAgc2V0OiBmdW5jdGlvbiBzZXQodmFsKSB7CiAgICAgICAgdGhpcy4kZW1pdCgidXBkYXRlOmRldmljZURldGFpbFZpc2libGUiLCB2YWwpOwogICAgICB9CiAgICB9LAogICAgdmlzaWJsZUFzc2V0RmllbGRzOiBmdW5jdGlvbiB2aXNpYmxlQXNzZXRGaWVsZHMoKSB7CiAgICAgIC8vIOiOt+WPlui1hOS6p+Wtl+auteWIl+ihqAogICAgICB2YXIgYXNzZXRMaXN0RGF0YSA9IHRoaXMuYXNzZXRMaXN0ICYmIHRoaXMuYXNzZXRMaXN0Lmxlbmd0aCA/IHRoaXMuYXNzZXRMaXN0WzBdLmZpZWxkc0l0ZW1zIDogW107CiAgICAgIHJldHVybiBhc3NldExpc3REYXRhLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLmlzU2hvdzsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6ZyA6KaB5a2X5YW46L2s5o2i55qE5a2X5q61CiAgICBkaWN0RmllbGRzOiBmdW5jdGlvbiBkaWN0RmllbGRzKCkgewogICAgICByZXR1cm4gWydpc1ZpcnR1YWwnLCAnZGVncmVlSW1wb3J0YW5jZScsICdpc1NwYXJpbmcnXTsKICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldERlcHRUcmVlKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXREaWN0RGF0YTogZnVuY3Rpb24gZ2V0RGljdERhdGEoZmllbGRLZXkpIHsKICAgICAgc3dpdGNoIChmaWVsZEtleSkgewogICAgICAgIGNhc2UgJ2lzVmlydHVhbCc6CiAgICAgICAgICByZXR1cm4gdGhpcy5kaWN0LnR5cGUuc3lzX3llc19ubzsKICAgICAgICBjYXNlICdkZWdyZWVJbXBvcnRhbmNlJzoKICAgICAgICAgIHJldHVybiB0aGlzLmRpY3QudHlwZS5pbXB0X2dyYWRlOwogICAgICAgIGNhc2UgJ2lzU3BhcmluZyc6CiAgICAgICAgICByZXR1cm4gdGhpcy5kaWN0LnR5cGUuaXNfc3BhcmluZzsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9LAogICAgLyoqIOafpeivouaJgOWxnumDqOmXqCAqL2dldERlcHRUcmVlOiBmdW5jdGlvbiBnZXREZXB0VHJlZSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgKDAsIF91c2VyLmdldEFsbERlcHRUcmVlKSgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgLy8g5bCG6YOo6Zeo5qCR57uT5p6E6L2s5o2i5Li65bmz57qn57uT5p6ECiAgICAgICAgdmFyIF9mbGF0dGVuRGVwdFRyZWUgPSBmdW5jdGlvbiBmbGF0dGVuRGVwdFRyZWUobm9kZXMpIHsKICAgICAgICAgIHZhciByZXN1bHQgPSBbXTsKICAgICAgICAgIG5vZGVzLmZvckVhY2goZnVuY3Rpb24gKG5vZGUpIHsKICAgICAgICAgICAgcmVzdWx0LnB1c2goewogICAgICAgICAgICAgIGlkOiBub2RlLmlkLAogICAgICAgICAgICAgIGxhYmVsOiBub2RlLmxhYmVsCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICByZXN1bHQgPSByZXN1bHQuY29uY2F0KF9mbGF0dGVuRGVwdFRyZWUobm9kZS5jaGlsZHJlbikpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICAgIHJldHVybiByZXN1bHQ7CiAgICAgICAgfTsKICAgICAgICBfdGhpcy5kZXB0T3B0aW9ucyA9IF9mbGF0dGVuRGVwdFRyZWUocmVzcG9uc2UuZGF0YSk7CiAgICAgIH0pOwogICAgfSwKICAgIGdldEFzc2V0OiBmdW5jdGlvbiBnZXRBc3NldCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgICgwLCBfb3ZlcnZpZXcuZ2V0QXNzZXRJbmZvKSh0aGlzLmFzc2V0SWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLmFzc2V0ID0gcmVzcG9uc2UuZGF0YSB8fCB7fTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0SXBNYWNMaXN0OiBmdW5jdGlvbiBnZXRJcE1hY0xpc3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmdJcE1hYyA9IHRydWU7CiAgICAgIHRoaXMucXVlcnlQYXJhbXNJcE1hYy5hc3NldElkID0gdGhpcy5hc3NldElkOwogICAgICAoMCwgX21hY0FpcC5saXN0TWFjQWlwKSh0aGlzLnF1ZXJ5UGFyYW1zSXBNYWMpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMzLmlwTWFjTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMzLnRvdGFsSXBNYWMgPSByZXNwb25zZS50b3RhbDsKICAgICAgICBfdGhpczMubG9hZGluZ0lwTWFjID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGdldEZpZWxkVmFsdWU6IGZ1bmN0aW9uIGdldEZpZWxkVmFsdWUoZmllbGQpIHsKICAgICAgaWYgKHRoaXMuZGljdEZpZWxkcy5pbmNsdWRlcyhmaWVsZCkpIHsKICAgICAgICByZXR1cm4gJyc7CiAgICAgIH0KICAgICAgdmFyIHZhbHVlID0gdGhpcy5hc3NldFtmaWVsZF07CiAgICAgIGlmIChmaWVsZCA9PT0gJ21haW50YWluVW5pdCcpIHsKICAgICAgICByZXR1cm4gdGhpcy5hc3NldC5tYWludGFpblVuaXROYW1lOwogICAgICB9CiAgICAgIGlmIChmaWVsZCA9PT0gJ2ZhY2lsaXR5TWFudWZhY3R1cmVyJykgewogICAgICAgIHJldHVybiB0aGlzLmFzc2V0LmZhY2lsaXR5TWFudWZhY3R1cmVyTmFtZSB8fCB2YWx1ZTsKICAgICAgfQogICAgICBpZiAoZmllbGQgPT09ICd2ZW5kb3InKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuYXNzZXQudmVuZG9yTmFtZSB8fCB2YWx1ZTsKICAgICAgfQogICAgICBpZiAoZmllbGQgPT09ICdhc3NldFR5cGUnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuYXNzZXQuYXNzZXRUeXBlRGVzYyB8fCB2YWx1ZTsKICAgICAgfQogICAgICBpZiAoZmllbGQgPT09ICdkb21haW5JZCcpIHsKICAgICAgICByZXR1cm4gdGhpcy5hc3NldC5kb21haW5OYW1lIHx8IHZhbHVlOwogICAgICB9CiAgICAgIGlmIChmaWVsZCA9PT0gJ2xvY2F0aW9uSWQnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuYXNzZXQubG9jYXRpb25GdWxsTmFtZSB8fCB2YWx1ZTsKICAgICAgfQogICAgICByZXR1cm4gdmFsdWU7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_overview", "require", "_macAip", "_assetRegister", "_interopRequireDefault", "_user", "mixins", "assetRegister", "name", "props", "assetId", "type", "String", "default", "assetName", "deviceDetailVisible", "Boolean", "assetAllocationType", "dicts", "data", "asset", "queryParamsIpMac", "pageNum", "pageSize", "ipMacList", "totalIpMac", "loadingIpMac", "deptOptions", "watch", "newVal", "getAsset", "getIpMacList", "computed", "visible", "get", "set", "val", "$emit", "visibleAssetFields", "assetListData", "assetList", "length", "fieldsItems", "filter", "item", "isShow", "dictFields", "created", "getDeptTree", "methods", "getDictData", "<PERSON><PERSON><PERSON>", "dict", "sys_yes_no", "impt_grade", "is_sparing", "_this", "getAllDeptTree", "then", "response", "flattenDeptTree", "nodes", "result", "for<PERSON>ach", "node", "push", "id", "label", "children", "concat", "_this2", "getAssetInfo", "_this3", "listMacAip", "rows", "total", "getFieldValue", "field", "includes", "value", "maintainUnitName", "facilityManufacturerName", "vendorName", "assetTypeDesc", "domainName", "locationFullName"], "sources": ["src/views/safe/networkdevices/networkDeviceDetail.vue"], "sourcesContent": ["<!-- 网络、安全设备详情 -->\n<template>\n  <el-dialog :visible.sync=\"visible\" :title=\"assetName\" width=\"50%\">\n    <div class=\"customForm-container\" style=\"padding: 0 20px\">\n      <div class=\"my-title\"><img src=\"@/assets/images/application/jbxx.png\" alt=\"\">基本信息</div>\n      <el-descriptions\n        class=\"custom-column\"\n        direction=\"vertical\"\n        size=\"medium\"\n        :colon=\"false\"\n        label-class-name=\"custom-label-style\"\n        content-class-name=\"custom-content-style\"\n        :column=\"3\"\n      >\n        <template v-for=\"item in visibleAssetFields\">\n          <el-descriptions-item :key=\"item.fieldKey\" :label=\"item.fieldName\">\n            <!-- 字典字段特殊处理 -->\n            <template v-if=\"dictFields.includes(item.fieldKey)\">\n              <span\n                v-for=\"dict in getDictData(item.fieldKey)\"\n                :key=\"dict.value\"\n                v-if=\"dict.value === asset[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <template v-if=\"['deptId', 'manageDeptId'].includes(item.fieldKey)\">\n              <span\n                v-for=\"dict in deptOptions\"\n                :key=\"dict.id\"\n                v-if=\"dict.id === asset[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <!-- 普通字段直接显示 -->\n            <template v-else>\n              {{ getFieldValue(item.fieldKey) }}\n            </template>\n          </el-descriptions-item>\n        </template>\n      </el-descriptions>\n\n      <div class=\"my-title\" style=\"margin: 20px 0\"><img src=\"@/assets/images/application/netinfo.png\" alt=\"\">网络信息\n      </div>\n      <el-table v-loading=\"loadingIpMac\" :data=\"ipMacList\">\n        <el-table-column label=\"所属网络\" prop=\"domainFullName\" show-overflow-tooltip/>\n        <el-table-column label=\"ipv4/ipv6\" prop=\"ipv4\"/>\n        <el-table-column label=\"MAC\" prop=\"mac\"/>\n        <el-table-column label=\"网关\" prop=\"defaultGateway\"/>\n        <el-table-column label=\"备注\" prop=\"remark\" show-overflow-tooltip/>\n      </el-table>\n      <pagination\n        v-show=\"totalIpMac>=0\"\n        :total=\"totalIpMac\"\n        :page.sync=\"queryParamsIpMac.pageNum\"\n        :limit.sync=\"queryParamsIpMac.pageSize\"\n        @pagination=\"getIpMacList\"\n      />\n    </div>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"visible = false\">取 消</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {getAssetInfo} from \"@/api/safe/overview\";\nimport {listMacAip} from \"@/api/safe/macAip\";\nimport assetRegister from \"@/mixins/assetRegister\";\nimport {getAllDeptTree} from \"@/api/system/user\";\n\nexport default {\n  mixins: [assetRegister],\n  name: \"networkDeviceDetail\",\n  props: {\n    assetId: {\n      type: String,\n      default: null\n    },\n    assetName: {\n      type: String,\n      default: null\n    },\n    deviceDetailVisible: {\n      type: Boolean,\n      default: false\n    },\n    assetAllocationType: {\n      type: String,\n      default: '4'\n    }\n  },\n  dicts: ['sys_yes_no', 'impt_grade', 'is_sparing'], // 保留字典配置\n  data() {\n    return {\n      asset: {},\n      queryParamsIpMac: {\n        pageNum: 1,\n        pageSize: 10,\n        assetId: null,\n      },\n      ipMacList: [],\n      totalIpMac: 0,\n      loadingIpMac: false,\n      deptOptions: [],\n    };\n  },\n  watch: {\n    assetId(newVal) {\n      if (newVal) {\n        this.getAsset();\n        this.getIpMacList();\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.deviceDetailVisible;\n      },\n      set(val) {\n        this.$emit(\"update:deviceDetailVisible\", val);\n      }\n    },\n    visibleAssetFields() {\n      // 获取资产字段列表\n      const assetListData = this.assetList && this.assetList.length ? this.assetList[0].fieldsItems : []\n      return assetListData.filter(item => item.isShow);\n    },\n\n    // 需要字典转换的字段\n    dictFields() {\n      return ['isVirtual', 'degreeImportance', 'isSparing'];\n    }\n  },\n  created() {\n    this.getDeptTree();\n  },\n  methods: {\n    getDictData(fieldKey) {\n      switch (fieldKey) {\n        case 'isVirtual':\n          return this.dict.type.sys_yes_no;\n        case 'degreeImportance':\n          return this.dict.type.impt_grade;\n          case 'isSparing':\n            return this.dict.type.is_sparing;\n        default:\n          return [];\n      }\n    },\n\n    /** 查询所属部门 */\n    getDeptTree() {\n      getAllDeptTree().then(response => {\n        // 将部门树结构转换为平级结构\n        const flattenDeptTree = (nodes) => {\n          let result = [];\n          nodes.forEach(node => {\n            result.push({\n              id: node.id,\n              label: node.label\n            });\n            if (node.children && node.children.length > 0) {\n              result = result.concat(flattenDeptTree(node.children));\n            }\n          });\n          return result;\n        };\n\n        this.deptOptions = flattenDeptTree(response.data);\n      });\n    },\n\n    getAsset() {\n      getAssetInfo(this.assetId).then(response => {\n        this.asset = response.data || {};\n      });\n    },\n    getIpMacList() {\n      this.loadingIpMac = true;\n      this.queryParamsIpMac.assetId = this.assetId;\n      listMacAip(this.queryParamsIpMac).then(response => {\n        this.ipMacList = response.rows;\n        this.totalIpMac = response.total;\n        this.loadingIpMac = false;\n      });\n    },\n    getFieldValue(field) {\n      if(this.dictFields.includes(field)){\n        return '';\n      }\n      const value = this.asset[field];\n      if (field === 'maintainUnit') {\n        return this.asset.maintainUnitName;\n      }\n      if (field === 'facilityManufacturer') {\n        return this.asset.facilityManufacturerName || value;\n      }\n      if (field === 'vendor') {\n        return this.asset.vendorName || value;\n      }\n      if (field === 'assetType') {\n        return this.asset.assetTypeDesc || value;\n      }\n      if (field === 'domainId') {\n        return this.asset.domainName || value;\n      }\n      if (field === 'locationId') {\n        return this.asset.locationFullName || value;\n      }\n      return value;\n    },\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n::v-deep .el-dialog__body {\n  padding: 0 0 20px 0 !important;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;AAqEA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,MAAA,GAAAC,sBAAA;EACAC,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,mBAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IACAI,mBAAA;MACAN,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAK,KAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,gBAAA;QACAC,OAAA;QACAC,QAAA;QACAb,OAAA;MACA;MACAc,SAAA;MACAC,UAAA;MACAC,YAAA;MACAC,WAAA;IACA;EACA;EACAC,KAAA;IACAlB,OAAA,WAAAA,QAAAmB,MAAA;MACA,IAAAA,MAAA;QACA,KAAAC,QAAA;QACA,KAAAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAnB,mBAAA;MACA;MACAoB,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,+BAAAD,GAAA;MACA;IACA;IACAE,kBAAA,WAAAA,mBAAA;MACA;MACA,IAAAC,aAAA,QAAAC,SAAA,SAAAA,SAAA,CAAAC,MAAA,QAAAD,SAAA,IAAAE,WAAA;MACA,OAAAH,aAAA,CAAAI,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,MAAA;MAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,QAAA;MACA,QAAAA,QAAA;QACA;UACA,YAAAC,IAAA,CAAAzC,IAAA,CAAA0C,UAAA;QACA;UACA,YAAAD,IAAA,CAAAzC,IAAA,CAAA2C,UAAA;QACA;UACA,YAAAF,IAAA,CAAAzC,IAAA,CAAA4C,UAAA;QACA;UACA;MACA;IACA;IAEA,aACAP,WAAA,WAAAA,YAAA;MAAA,IAAAQ,KAAA;MACA,IAAAC,oBAAA,IAAAC,IAAA,WAAAC,QAAA;QACA;QACA,IAAAC,gBAAA,YAAAA,gBAAAC,KAAA;UACA,IAAAC,MAAA;UACAD,KAAA,CAAAE,OAAA,WAAAC,IAAA;YACAF,MAAA,CAAAG,IAAA;cACAC,EAAA,EAAAF,IAAA,CAAAE,EAAA;cACAC,KAAA,EAAAH,IAAA,CAAAG;YACA;YACA,IAAAH,IAAA,CAAAI,QAAA,IAAAJ,IAAA,CAAAI,QAAA,CAAA3B,MAAA;cACAqB,MAAA,GAAAA,MAAA,CAAAO,MAAA,CAAAT,gBAAA,CAAAI,IAAA,CAAAI,QAAA;YACA;UACA;UACA,OAAAN,MAAA;QACA;QAEAN,KAAA,CAAA7B,WAAA,GAAAiC,gBAAA,CAAAD,QAAA,CAAAxC,IAAA;MACA;IACA;IAEAW,QAAA,WAAAA,SAAA;MAAA,IAAAwC,MAAA;MACA,IAAAC,sBAAA,OAAA7D,OAAA,EAAAgD,IAAA,WAAAC,QAAA;QACAW,MAAA,CAAAlD,KAAA,GAAAuC,QAAA,CAAAxC,IAAA;MACA;IACA;IACAY,YAAA,WAAAA,aAAA;MAAA,IAAAyC,MAAA;MACA,KAAA9C,YAAA;MACA,KAAAL,gBAAA,CAAAX,OAAA,QAAAA,OAAA;MACA,IAAA+D,kBAAA,OAAApD,gBAAA,EAAAqC,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAAhD,SAAA,GAAAmC,QAAA,CAAAe,IAAA;QACAF,MAAA,CAAA/C,UAAA,GAAAkC,QAAA,CAAAgB,KAAA;QACAH,MAAA,CAAA9C,YAAA;MACA;IACA;IACAkD,aAAA,WAAAA,cAAAC,KAAA;MACA,SAAA/B,UAAA,CAAAgC,QAAA,CAAAD,KAAA;QACA;MACA;MACA,IAAAE,KAAA,QAAA3D,KAAA,CAAAyD,KAAA;MACA,IAAAA,KAAA;QACA,YAAAzD,KAAA,CAAA4D,gBAAA;MACA;MACA,IAAAH,KAAA;QACA,YAAAzD,KAAA,CAAA6D,wBAAA,IAAAF,KAAA;MACA;MACA,IAAAF,KAAA;QACA,YAAAzD,KAAA,CAAA8D,UAAA,IAAAH,KAAA;MACA;MACA,IAAAF,KAAA;QACA,YAAAzD,KAAA,CAAA+D,aAAA,IAAAJ,KAAA;MACA;MACA,IAAAF,KAAA;QACA,YAAAzD,KAAA,CAAAgE,UAAA,IAAAL,KAAA;MACA;MACA,IAAAF,KAAA;QACA,YAAAzD,KAAA,CAAAiE,gBAAA,IAAAN,KAAA;MACA;MACA,OAAAA,KAAA;IACA;EACA;AACA", "ignoreList": []}]}
{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\application\\applicationForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\application\\applicationForm.vue", "mtime": 1754969114060}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Z2V0QXBwbGljYXRpb25JbmZvLCBhZGRBcHBsaWNhdGlvbkluZm8sIHVwZGF0ZUFwcGxpY2F0aW9uSW5mbywgZ2V0QXBwbGljYXRpb259IGZyb20gIkAvYXBpL3NhZmUvYXBwbGljYXRpb24iOwppbXBvcnQgQXBwbGljYXRpb25MaW5rIGZyb20gJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYXBwbGljYXRpb25MaW5rJzsKaW1wb3J0IEFwcGxpY2F0aW9uU2l0ZSBmcm9tICdAL3ZpZXdzL2hobENvZGUvY29tcG9uZW50L2FwcGxpY2F0aW9uL2FwcGxpY2F0aW9uU2l0ZSc7CmltcG9ydCBVc2VyU2VsZWN0IGZyb20gJ0Avdmlld3MvaGhsQ29kZS9jb21wb25lbnQvdXNlclNlbGVjdCc7CmltcG9ydCBEZXB0U2VsZWN0IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvZGVwdFNlbGVjdCc7CmltcG9ydCBOZXR3b3JrU2VsZWN0IGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9zZWxlY3QvbmV0d29ya1NlbGVjdCc7CmltcG9ydCBEeW5hbWljVGFnIGZyb20gJ0AvY29tcG9uZW50cy9EeW5hbWljVGFnJzsKaW1wb3J0IFZlbmRvclNlbGVjdDIgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC92ZW5kb3JTZWxlY3QyJzsKaW1wb3J0IERpY3RTZWxlY3QgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC9kaWN0U2VsZWN0JzsKaW1wb3J0IHtnZXRWYWxGcm9tT2JqZWN0fSBmcm9tICJAL3V0aWxzIjsKaW1wb3J0IHt3YWl0Rm9yVmFsdWV9IGZyb20gIkAvdXRpbHMvcnVveWkiOwppbXBvcnQge2xpc3RWZW5kb3JCeUFwcGxpY2F0aW9ufSBmcm9tICJAL2FwaS9zYWZlL3ZlbmRvciI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImFwcGxpY2F0aW9uRm9ybSIsCiAgY29tcG9uZW50czogewogICAgQXBwbGljYXRpb25MaW5rLAogICAgQXBwbGljYXRpb25TaXRlLAogICAgVXNlclNlbGVjdCwKICAgIERlcHRTZWxlY3QsCiAgICBOZXR3b3JrU2VsZWN0LAogICAgRGljdFNlbGVjdCwKICAgIER5bmFtaWNUYWcsCiAgICBWZW5kb3JTZWxlY3QyLAogIH0sCiAgZGljdHM6IFsKICAgICdzeXNfeWVzX25vJywKICAgICdhcHBfbmV0X3NjYWxlJywKICAgICdjb25zdHJ1Y3RfdHlwZScsCiAgICAnc3lzdGVtX3R5cGUnLAogICAgJ3Byb3RlY3Rpb25fZ3JhZGUnLAogICAgJ2Fzc2V0X3N0YXRlJywKICAgICdhcHBfbG9naW5fdHlwZScsCiAgICAnYXBwX3RlY2huaWNhbCcsCiAgICAnYXBwX2RlcGxveScsCiAgICAnYXBwX3N0b3JhZ2UnLAogICAgJ2V2YWx1YXRpb25fcmVzdWx0cycsCiAgICAnZXZhbHVhdGlvbl9zdGF0dXMnCiAgXSwKICBpbmplY3Q6IHsKICAgICRlZGl0YWJsZTogewogICAgICBkZWZhdWx0OiB7dmFsdWU6IHRydWV9LAogICAgfQogIH0sCiAgcHJvcHM6IHsKICAgIGFzc2V0SWQ6IHsKICAgICAgdHlwZTogW1N0cmluZywgTnVtYmVyXSwKICAgICAgcmVxdWlyZWQ6IGZhbHNlLAogICAgICBkZWZhdWx0OiBudWxsLAogICAgfSwKICAgIGNoYW5nZUlkOiBGdW5jdGlvbiwKICAgIHJlYWRvbmx5OiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICAgIGRpc2FibGVkOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjb2xsYXBzZU5hbWVzOiBbJzEnLCAnMicsICczJywgJzQnLCAnNSddLAogICAgICB2ZW5kb3JzZGF0YTogJzEnLAogICAgICB1c2VyZGF0YTogJzEnLAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIGFzc2V0TmFtZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5bqU55So5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICAgIHttaW46IDAsIG1heDogNjQsIG1lc3NhZ2U6ICflupTnlKjlkI3np7DkuI3og73otoXov4cgNjQg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIGFzc2V0Q2xhc3M6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui1hOS6p+WIhuexu+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICBkb21haW5Vcmw6IFsKICAgICAgICAgIHttaW46IDAsIG1heDogMTI4LCBtZXNzYWdlOiAn5Z+f5ZCN5LiN6IO96LaF6L+HIDEyOCDkuKrlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgICAgewogICAgICAgICAgICBwYXR0ZXJuOiAvXig/PV4uezMsMjU1fSQpKGh0dHAocyk/OlwvXC8pPyh3d3dcLik/W2EtekEtWjAtOV1bLWEtekEtWjAtOV17MCw2Mn0oXC5bYS16QS1aMC05XVstYS16QS1aMC05XXswLDYyfSkrKDpcZCspKihcL1x3K1wuXHcrKSokLywKICAgICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeato+ehrueahOWfn+WQjSIsCiAgICAgICAgICAgIHRyaWdnZXI6IFsnYmx1cicsICdjaGFuZ2UnXQogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICAgIG1hbmFnZXI6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui0n+i0o+S6uuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICB1c2VySWQ6IFsKICAgICAgICAgIC8vIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLdJROS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBkZXB0SWQ6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICBwaG9uZTogWwogICAgICAgICAge21pbjogMCwgbWF4OiAxMSwgbWVzc2FnZTogJ+iBlOezu+eUteivneS4jeiDvei2hei/hyAxMSDkvY0nLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgICAge3BhdHRlcm46IC9eMVsxfDJ8M3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sIG1lc3NhZ2U6ICLor7fovpPlhaXmraPnoa7nmoTogZTns7vnlLXor50iLCB0cmlnZ2VyOiBbJ2JsdXInLCAnY2hhbmdlJ119LAogICAgICAgIF0sCiAgICAgICAgdXJsOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnmbvlvZXlnLDlnYDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgICAge21pbjogMCwgbWF4OiAxMjgsIG1lc3NhZ2U6ICfnmbvlvZXlnLDlnYDkuI3og73otoXov4cgMTI4IOS4quWtl+espicsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgXSwKICAgICAgICBpcGQ6IFsKICAgICAgICAgIHttaW46IDAsIG1heDogMzIwLCBtZXNzYWdlOiAnSVDlnLDlnYDmrrXloavlhpnlt7LkuIrpmZAnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAiSXDlnLDlnYDmrrXkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9CiAgICAgICAgXSwKICAgICAgICBuZXRNZW1vOiBbCiAgICAgICAgICB7bWluOiAwLCBtYXg6IDI1NSwgbWVzc2FnZTogJ+aLk+aJkeWbvuivtOaYjuS4jeiDvei2hei/hyAyNTUg5Liq5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIHRhZ3M6IFsKICAgICAgICAgIHttaW46IDAsIG1heDogMjU1LCBtZXNzYWdlOiAn5ouT5omR5Zu+6K+05piO5LiN6IO96LaF6L+HIDI1NSDkuKrlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIGd2OiBnZXRWYWxGcm9tT2JqZWN0LAogICAgICBkZXBsb3lMb2NhdGlvbjogbG9jYWxTdG9yYWdlLmdldEl0ZW0oImRsIiksCiAgICAgIG1hbmFnZXJMYWJlbDogJ+i0o+S7u+S6ui/nlLXor50nLAogICAgICBtYW5hZ2VQbGFjZWhvbGRlcjogJ+ivt+mAieaLqei0o+S7u+S6uicKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgIGlmICh0aGlzLmRlcGxveUxvY2F0aW9uID09PSAnZmFpcicpIHsKICAgICAgICB0aGlzLm1hbmFnZXJMYWJlbCA9ICfotKPku7vmsJHoraYv55S16K+dJwogICAgICAgIHRoaXMubWFuYWdlUGxhY2Vob2xkZXIgPSAn6K+36YCJ5oup6LSj5Lu75rCR6K2mJwogICAgICB9CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5pbml0KCkKICAgIH0pOwogIH0sCiAgYWN0aXZhdGVkKCkgewogICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuaW5pdCgpCiAgICB9KTsKICB9LAoKICBtZXRob2RzOiB7CiAgICAvL+S/oeWIm+mAgumFjeaXtumXtOaYvuekugogICAgc2hvd0RhcHQoKSB7CiAgICAgIGlmICh0aGlzLmZvcm0uaXNhZGFwdCA9PSAnTicpIHsKICAgICAgICB0aGlzLmZvcm0uYWRhcHREYXRlID0gbnVsbDsKICAgICAgfQogICAgfSwKICAgIC8v5a+G5bGP5bqU55So5bu66K6+5pe26Ze05pi+56S6CiAgICBzaG93Q2lwaGVyKCkgewogICAgICBpZiAodGhpcy5mb3JtLmlzY2lwaGVyID09ICdOJykgewogICAgICAgIHRoaXMuZm9ybS5jaXBoZXJEYXRlID0gbnVsbDsKICAgICAgfQogICAgfSwKCiAgICBzZWxlY3RWZW5kb3IocGFyYW1zKSB7CiAgICAgIGlmICh0aGlzLmZvcm0udmVuZG9ycyA9PSBudWxsIHx8IHRoaXMuZm9ybS52ZW5kb3JzID09ICcnKSB7CiAgICAgICAgdGhpcy52ZW5kb3JzZGF0YSA9IG51bGw7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy52ZW5kb3JzZGF0YSA9ICcxJzsKICAgICAgfQogICAgICByZXR1cm4gbGlzdFZlbmRvckJ5QXBwbGljYXRpb24oewogICAgICAgIGFwcGxpY2F0aW9uSWQ6IHRoaXMuYXNzZXRJZCwKICAgICAgICBhcHBsaWNhdGlvbkNvZGU6IHRoaXMuZm9ybS52ZW5kb3JzLAogICAgICAgIC4uLnBhcmFtcwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yid5aeL5YyWICovCiAgICBpbml0KCkgewogICAgICAvLyBsZXQgcGFyYW1zID0gdGhpcy4kcm91dGUucXVlcnk7CiAgICAgIGlmICh0aGlzLmFzc2V0SWQpIHsKICAgICAgICB0aGlzLmZvcm0uYXNzZXRJZCA9IHRoaXMuYXNzZXRJZDsKICAgICAgfQogICAgICBpZiAodGhpcy5mb3JtLmFzc2V0SWQgIT0gbnVsbCkgewogICAgICAgIGdldEFwcGxpY2F0aW9uSW5mbyh0aGlzLmZvcm0uYXNzZXRJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZyhyZXNwb25zZS5kYXRhLCdkYXRhJykKICAgICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICB3YWl0Rm9yVmFsdWUoKCkgPT4gZ2V0VmFsRnJvbU9iamVjdCgnc2l0ZScsIHRoaXMuJHJlZnMsIG51bGwpKS50aGVuKHNpdGUgPT4gewogICAgICAgICAgICBpZighc2l0ZSl7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmKHNpdGUgaW5zdGFuY2VvZiBBcnJheSl7CiAgICAgICAgICAgICAgc2l0ZS5mb3JFYWNoKGl0ZW0gPT4gaXRlbS5nZXRMaXN0KCkpOwogICAgICAgICAgICB9ZWxzZSB7CiAgICAgICAgICAgICAgc2l0ZS5nZXRMaXN0KCkKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKCiAgICAvLyDmoKHpqozmlbDmja4KICAgIHZhbGlkYXRlRm9ybSgpIHsKICAgICAgbGV0IGZsYWcgPSBudWxsCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBmbGFnID0gdHJ1ZQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBmbGFnID0gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICAgIHJldHVybiBmbGFnCiAgICB9LAoKICAgIC8qKiDkv53lrZjmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVNhdmUoKSB7CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybSwnZmZmZicpCiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7CiAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICAgIGxldCBsaW5rID0gdGhpcy5maWx0ZXJOdWxsKHRoaXMuZm9ybS5saW5rcykKICAgICAgICAgIGxpbmsuZm9yRWFjaChsID0+IHsKICAgICAgICAgICAgaWYgKCEobC5saW5rSXAgJiYgbC5saW5rSXAubGVuZ3RoID4gMCkpIHsKICAgICAgICAgICAgICBsLmxpbmtJcCA9IG51bGw7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKCEobC5saW5rUG9ydCAmJiBsLmxpbmtQb3J0Lmxlbmd0aCA+IDApKSB7CiAgICAgICAgICAgICAgbC5saW5rUG9ydCA9IG51bGw7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLmxpbmtzID0gbGluazsKICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5hc3NldElkICE9IG51bGwpIHsKICAgICAgICAgICAgICB1cGRhdGVBcHBsaWNhdGlvbkluZm8odGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgICAgdGhpcy5pbml0KCkKICAgICAgICAgICAgICAgIHJldHVybiByZXNvbHZlKCk7CiAgICAgICAgICAgICAgfSkuY2F0Y2goKGVycikgPT4gewogICAgICAgICAgICAgICAgcmV0dXJuIHJlamVjdChlcnIpOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGFkZEFwcGxpY2F0aW9uSW5mbyh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgICAgdGhpcy5mb3JtLmFzc2V0SWQgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICAgICAgdGhpcy5jaGFuZ2VJZChyZXNwb25zZS5kYXRhKTsKICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgICAgdGhpcy5pbml0KCkKICAgICAgICAgICAgICAgIHJldHVybiByZXNvbHZlKCk7CiAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgICAgIHJldHVybiByZWplY3QoZXJyKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnV2FybmluZygi5Y+C5pWw5qCh6aqM5aSx6LSl77yM6K+36L6T5YWl5q2j56Gu55qE5Y+C5pWw77yBIik7CiAgICAgICAgICAgIHJldHVybiByZWplY3QoKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSkKICAgIH0sCiAgICAvKiog6KGo5Y2V6YeN572uICovCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGFzc2V0SWQ6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldENvZGU6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldE5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBzb2Z0d2FyZVZlcnNpb246IHVuZGVmaW5lZCwKICAgICAgICBkZWdyZWVJbXBvcnRhbmNlOiB1bmRlZmluZWQsCiAgICAgICAgbWFuYWdlcjogdW5kZWZpbmVkLAogICAgICAgIGRvbWFpblVybDogdW5kZWZpbmVkLAogICAgICAgIHN5c3RlbVR5cGU6IHVuZGVmaW5lZCwKICAgICAgICBwaG9uZTogdW5kZWZpbmVkLAogICAgICAgIGFzc2V0VHlwZTogdW5kZWZpbmVkLAogICAgICAgIGFzc2V0VHlwZURlc2M6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldENsYXNzOiB1bmRlZmluZWQsCiAgICAgICAgYXNzZXRDbGFzc0Rlc2M6IHVuZGVmaW5lZCwKICAgICAgICBjb25zdHJ1Y3Q6IHVuZGVmaW5lZCwKICAgICAgICBuZXRUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgYXBwVHlwZTogdW5kZWZpbmVkLAogICAgICAgIHNlcnZpY2VHcm91cDogdW5kZWZpbmVkLAogICAgICAgIGZyZXF1ZW5jeTogdW5kZWZpbmVkLAogICAgICAgIHVzYWdlQ291bnQ6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyU2NhbGU6IHVuZGVmaW5lZCwKICAgICAgICB1c2VyT2JqZWN0OiB1bmRlZmluZWQsCiAgICAgICAgdXJsOiB1bmRlZmluZWQsCiAgICAgICAgaXBkOiB1bmRlZmluZWQsCiAgICAgICAgdGVjaG5pY2FsOiB1bmRlZmluZWQsCiAgICAgICAgZGVwbG95OiB1bmRlZmluZWQsCiAgICAgICAgc3RvcmFnZTogdW5kZWZpbmVkLAogICAgICAgIG5ldGVudjogdW5kZWZpbmVkLAogICAgICAgIGlza2V5OiB1bmRlZmluZWQsCiAgICAgICAgZGF0YW51bTogdW5kZWZpbmVkLAogICAgICAgIGlzYmFzZTogIjAiLAogICAgICAgIGlzbGluazogdW5kZWZpbmVkLAogICAgICAgIGlzaGFyZTogdW5kZWZpbmVkLAogICAgICAgIGlzbG9nOiB1bmRlZmluZWQsCiAgICAgICAgaXNwbGFuOiB1bmRlZmluZWQsCiAgICAgICAgaXNhZGFwdDogdW5kZWZpbmVkLAogICAgICAgIGlzY2lwaGVyOiB1bmRlZmluZWQsCiAgICAgICAgYWRhcHREYXRlOiB1bmRlZmluZWQsCiAgICAgICAgY2lwaGVyRGF0ZTogdW5kZWZpbmVkLAogICAgICAgIGZ1bmN0aW9uOiB1bmRlZmluZWQsCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsCiAgICAgICAgdXNlcklkOiB1bmRlZmluZWQsCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsCiAgICAgICAgb3JnbklkOiB1bmRlZmluZWQsCiAgICAgICAgdmVuZG9yczogdW5kZWZpbmVkLAogICAgICAgIHVwVGltZTogdW5kZWZpbmVkLAogICAgICAgIGR3aWQ6IHVuZGVmaW5lZCwKICAgICAgICBjb250YWN0b3I6IHVuZGVmaW5lZCwKICAgICAgICBkb21haW5JZDogdW5kZWZpbmVkLAogICAgICAgIG5ldFNjYWxlOiB1bmRlZmluZWQsCiAgICAgICAgbmV0VG9wbzogdW5kZWZpbmVkLAogICAgICAgIG5ldE1lbW86IHVuZGVmaW5lZCwKICAgICAgICB3YWl0aW5nSW5zdXJhbmNlRmlsaW5nTnVtYmVyOiB1bmRlZmluZWQsCiAgICAgICAgd2FpdGluZ0luc3VyYW5jZUZpbGluZ1RpbWU6IHVuZGVmaW5lZCwKICAgICAgICB3YWl0aW5nSW5zdXJhbmNlRmlsaW5nU2NhbjogdW5kZWZpbmVkLAogICAgICAgIHByb2R1Y3ROYW1lOiB1bmRlZmluZWQsCiAgICAgICAgdmVyc2lvbk51bWJlcjogdW5kZWZpbmVkLAogICAgICAgIHRhZ3M6ICIiLAogICAgICAgIGxpbmtzOiBbXSwKICAgICAgICBlaWRzOiBbXSwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog55So5oi36YCJ5oupICovCiAgICBoYW5kbGVVc2VyU2VsZWN0KHZhbCkgewogICAgICBpZiAodGhpcy5mb3JtLm1hbmFnZXIgPT0gbnVsbCB8fCB0aGlzLmZvcm0ubWFuYWdlciA9PSAnJykgewogICAgICAgIHRoaXMudXNlcmRhdGEgPSBudWxsOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMudXNlcmRhdGEgPSAnMSc7CiAgICAgIH0KICAgICAgdGhpcy5mb3JtLnBob25lID0gdmFsOwogICAgfSwKICAgIC8qKiDov4fmu6TnqbrlgLwgKi8KICAgIGZpbHRlck51bGwodmFsdWUpIHsKICAgICAgLy8gcmV0dXJuIHZhbHVlLmZpbHRlcigoaXRlbSkgPT4gSlNPTi5zdHJpbmdpZnkoaXRlbSkgIT09ICd7fScpOwogICAgICByZXR1cm4gdmFsdWUuZmlsdGVyKChpdGVtKSA9PiBPYmplY3Qua2V5cyhpdGVtKS5sZW5ndGggIT09IDApOwogICAgfSwKICB9LAp9Cg=="}, {"version": 3, "sources": ["applicationForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAicA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "applicationForm.vue", "sourceRoot": "src/views/hhlCode/component/application", "sourcesContent": ["<template>\n  <div>\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" :disabled=\"!$editable.value\">\n      <el-row>\n        <el-col :span=\"22\">\n          <el-row :gutter=\"10\" type=\"flex\" style=\"flex-wrap: wrap;margin-bottom: 10px;\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">基本信息</div>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"系统名称\" prop=\"assetName\">\n                <el-input v-model=\"form.assetName\" placeholder=\"请输入应用名称\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"登录地址\" prop=\"url\">\n                <el-input v-model=\"form.url\" placeholder=\"请输入登录地址\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"域名\" prop=\"domainUrl\">\n                <el-input v-model=\"form.domainUrl\" placeholder=\"请输入域名\"/>\n              </el-form-item>\n            </el-col>\n\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"IP地址段\" prop=\"ipd\">\n                <el-input :autosize=\"{minRows: 3, maxRows: 3}\" v-model=\"form.ipd\" type=\"textarea\"\n                          placeholder=\"请输入IP地址段，连续IP地址段格式为“***********-100”，单个IP需用分隔符号“,”。\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"所属单位\" prop=\"deptId\">\n                <dept-select v-model=\"form.deptId\" is-current :isAllData=\"!$editable.value\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item :label=\"managerLabel\" prop=\"manager\">\n                <user-select v-model=\"form.manager\" :placeholder=\"managePlaceholder\" :userdata=\"userdata\" multiple\n                             @setPhone=\"handleUserSelect\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"系统类型\" prop=\"systemType\">\n                <el-select v-model=\"form.systemType\" placeholder=\"请选择系统类型\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.system_type\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"Number(dict.value)\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"建设模式\" prop=\"construct\">\n                <el-select v-model=\"form.construct\" placeholder=\"请选择建设模式\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.construct_type\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"登录方式\" prop=\"loginType\">\n                <el-select v-model=\"form.loginType\" placeholder=\"请选择登录方式\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.app_login_type\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"技术架构\" prop=\"technical\">\n                <el-select v-model=\"form.technical\" placeholder=\"请选择技术架构\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.app_technical\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"主部署方式\" prop=\"deploy\">\n                <el-select v-model=\"form.deploy\" placeholder=\"请选择主部署方式\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.app_deploy\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"主部署网络\" prop=\"domainId\">\n                <NetworkSelect v-model=\"form.domainId\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"上线状态\" prop=\"state\">\n                <el-select v-model=\"form.state\" placeholder=\"请选择上线状态\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.asset_state\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"上线时间\" prop=\"uodTime\">\n                <el-date-picker\n                  v-model=\"form.uodTime\"\n                  type=\"date\"\n                  placeholder=\"选择上线时间\"\n                  format=\"yyyy 年 MM 月 dd 日\"\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\n\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"关键基础设施\" prop=\"iskey\">\n                <el-radio-group v-model=\"form.iskey\">\n                  <el-radio\n                    v-for=\"dict in dict.type.sys_yes_no\"\n                    :key=\"dict.value\"\n                    :label=\"dict.value\"\n\n                  >{{ dict.label }}\n                  </el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"开发合作企业\" prop=\"vendor\">\n                <VendorSelect2 v-model=\"form.vendors\" multiple placeholder=\"请选择开发合作企业\"\n                               :vendorsdata=\"vendorsdata\" :selectVendor=\"selectVendor\" :isDisabled=\"!$editable.value\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"产品名称\">\n                <el-input v-model=\"form.productName\" placeholder=\"请输入产品名称\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"8\">\n              <el-form-item label=\"版本号\">\n                <el-input v-model=\"form.versionNumber\" placeholder=\"请输入版本号\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"自定义标签\" prop=\"tags\">\n                <Dynamic-Tag v-model=\"form.tags\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-divider class=\"hr\"></el-divider>\n          <el-row :gutter=\"10\" type=\"flex\" style=\"flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">备案信息</div>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"等保级别\" prop=\"protectGrade\">\n                <el-select v-model=\"form.protectGrade\" placeholder=\"请选择等保级别\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.protection_grade\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"备案编号\">\n                <el-input v-model=\"form.waitingInsuranceFilingNumber\" placeholder=\"请输入备案编号\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"备案时间\">\n                <el-date-picker\n                  v-model=\"form.waitingInsuranceFilingTime\"\n                  type=\"date\"\n                  placeholder=\"选择备案时间\"\n                  format=\"yyyy 年 MM 月 dd 日\"\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\n\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"备案扫描件\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"form.waitingInsuranceFilingScan\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-divider class=\"hr\"></el-divider>\n          <el-row :gutter=\"10\" type=\"flex\" style=\"flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">测评信息</div>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"测评年份\">\n                <el-date-picker\n                  v-model=\"form.evaluationYear\"\n                  type=\"year\"\n                  placeholder=\"请选择测评年份\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"测评机构名称\">\n                <el-input v-model=\"form.evaluationAgency\" placeholder=\"请输入测评机构名称\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"测评报告编号\">\n                <el-input v-model=\"form.evaluationNumber\" placeholder=\"请输入测评报告编号\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"测评结论\">\n<!--                <el-input v-model=\"form.evaluationResults\" placeholder=\"请输入测评结论\"/>-->\n                <el-select v-model=\"form.evaluationResults\" placeholder=\"请选择等保级别\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.evaluation_results\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"测评得分\">\n                <el-input v-model=\"form.evaluationScore\" placeholder=\"请输入测评得分\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"状态\">\n<!--                <el-input v-model=\"form.evaluationstatus\" placeholder=\"请选择状态\"/>-->\n                <el-select v-model=\"form.evaluationstatus\" placeholder=\"请选择状态\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.evaluation_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"测评报告\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"form.evaluationReport\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-divider class=\"hr\"></el-divider>\n          <el-row :gutter=\"10\" type=\"flex\" style=\"flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">外部连接信息</div>\n            </el-col>\n            <el-col :span=\"24\">\n              <ApplicationLink :disabled=\"!$editable.value\" v-model=\"form.links\"/>\n            </el-col>\n          </el-row>\n          <el-divider class=\"hr\"></el-divider>\n          <el-row :gutter=\"10\" type=\"flex\" style=\"flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">拓扑结构信息</div>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"网络拓扑图\" prop=\"netTopo\">\n                <file-upload\n                  :disUpload=\"!$editable.value\"\n                  v-model=\"form.netTopo\"\n                  :limit=\"5\"\n                  :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"拓扑图说明\" prop=\"netMemo\">\n                <el-input v-model=\"form.netMemo\" :autosize=\"{minRows: 6, maxRows: 6}\" type=\"textarea\"\n                          placeholder=\"请输入拓扑图说明\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-divider class=\"hr\"></el-divider>\n          <el-row :gutter=\"10\" type=\"flex\" style=\"flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">运营维护情况</div>\n            </el-col>\n            <el-col :span=\"24\">\n              <ApplicationSite ref=\"site\" :disabled=\"!$editable.value\" :value.sync=\"form.eids\" :asset-id=\"form.assetId\" multiple/>\n            </el-col>\n          </el-row>\n          <el-divider class=\"hr\"></el-divider>\n          <el-row :gutter=\"0\" type=\"flex\" style=\"flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;width: 50%;\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">其他基本信息</div>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"基层数据采集\">\n                <el-radio-group v-model=\"form.isbase\">\n                  <el-radio\n                    v-for=\"dict in dict.type.sys_yes_no\"\n                    :key=\"dict.value\"\n                    :label=\"dict.value\"\n                  >{{ dict.label }}\n                  </el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"日志安审平台\" prop=\"islog\">\n                <el-radio-group v-model=\"form.islog\">\n                  <el-radio\n                    v-for=\"dict in dict.type.sys_yes_no\"\n                    :key=\"dict.value\"\n                    :label=\"dict.value\"\n                  >{{ dict.label }}\n                  </el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"是否信创适配\" prop=\"isadapt\">\n                <el-radio-group v-model=\"form.isadapt\">\n                  <el-radio\n                    v-for=\"dict in dict.type.sys_yes_no\"\n                    :key=\"dict.value\"\n                    :label=\"dict.value\"\n                    @change=\"showDapt()\">{{ dict.label }}\n                  </el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"信创适配时间\" prop=\"adaptDate\" v-if=\"form.isadapt=='Y'\">\n                <el-date-picker\n                  clearable\n                  v-model=\"form.adaptDate\"\n                  type=\"date\"\n                  format=\"yyyy 年 MM 月 dd 日\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"请选择信创适配时间\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"是否密码应用\" prop=\"iscipher\">\n                <el-radio-group v-model=\"form.iscipher\">\n                  <el-radio\n                    v-for=\"dict in dict.type.sys_yes_no\"\n                    :key=\"dict.value\"\n                    :label=\"dict.value\"\n                    @change=\"showCipher()\">{{ dict.label }}\n                  </el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"密码应用建设时间\" prop=\"cipherDate\" v-if=\"form.iscipher=='Y'\">\n                <el-date-picker\n                  clearable\n                  v-model=\"form.cipherDate\"\n                  type=\"date\"\n                  format=\"yyyy 年 MM 月 dd 日\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"请选择建设时间\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"升级改造计划\" prop=\"isplan\">\n                <el-radio-group v-model=\"form.isplan\">\n                  <el-radio\n                    v-for=\"dict in dict.type.sys_yes_no\"\n                    :key=\"dict.value\"\n                    :label=\"dict.value\"\n                  >{{ dict.label }}\n                  </el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\" v-if=\"deployLocation === 'fair'\">\n              <el-form-item label=\"警综对接\" prop=\"islink\">\n                <el-radio-group v-model=\"form.islink\">\n                  <el-radio\n                    v-for=\"dict in dict.type.sys_yes_no\"\n                    :key=\"dict.value\"\n                    :label=\"dict.value\"\n                  >{{ dict.label }}\n                  </el-radio>\n                </el-radio-group>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nimport {getApplicationInfo, addApplicationInfo, updateApplicationInfo, getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {getValFromObject} from \"@/utils\";\nimport {waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\n\nexport default {\n  name: \"applicationForm\",\n  components: {\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  data() {\n    return {\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        assetName: [\n          {required: true, message: \"应用名称不能为空\", trigger: \"blur\"},\n          {min: 0, max: 64, message: '应用名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetClass: [\n          {required: true, message: \"资产分类不能为空\", trigger: \"blur\"},\n        ],\n        domainUrl: [\n          {min: 0, max: 128, message: '域名不能超过 128 个字符', trigger: 'blur'},\n          {\n            pattern: /^(?=^.{3,255}$)(http(s)?:\\/\\/)?(www\\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\\d+)*(\\/\\w+\\.\\w+)*$/,\n            message: \"请输入正确的域名\",\n            trigger: ['blur', 'change']\n          },\n        ],\n        manager: [\n          {required: true, message: \"负责人不能为空\", trigger: \"blur\"},\n        ],\n        userId: [\n          // { required: true, message: \"用户ID不能为空\", trigger: \"blur\" }\n        ],\n        deptId: [\n          {required: true, message: \"单位不能为空\", trigger: \"blur\"},\n        ],\n        phone: [\n          {min: 0, max: 11, message: '联系电话不能超过 11 位', trigger: 'blur'},\n          {pattern: /^1[1|2|3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的联系电话\", trigger: ['blur', 'change']},\n        ],\n        url: [\n          {required: true, message: \"登录地址不能为空\", trigger: \"blur\"},\n          {min: 0, max: 128, message: '登录地址不能超过 128 个字符', trigger: 'blur'},\n        ],\n        ipd: [\n          {min: 0, max: 320, message: 'IP地址段填写已上限', trigger: 'blur'},\n          {required: true, message: \"Ip地址段不能为空\", trigger: \"blur\"}\n        ],\n        netMemo: [\n          {min: 0, max: 255, message: '拓扑图说明不能超过 255 个字符', trigger: 'blur'},\n        ],\n        tags: [\n          {min: 0, max: 255, message: '拓扑图说明不能超过 255 个字符', trigger: 'blur'},\n        ],\n      },\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人'\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n\n  methods: {\n    //信创适配时间显示\n    showDapt() {\n      if (this.form.isadapt == 'N') {\n        this.form.adaptDate = null;\n      }\n    },\n    //密屏应用建设时间显示\n    showCipher() {\n      if (this.form.iscipher == 'N') {\n        this.form.cipherDate = null;\n      }\n    },\n\n    selectVendor(params) {\n      if (this.form.vendors == null || this.form.vendors == '') {\n        this.vendorsdata = null;\n      } else {\n        this.vendorsdata = '1';\n      }\n      return listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        ...params\n      });\n    },\n    /** 初始化 */\n    init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        this.form.assetId = this.assetId;\n      }\n      if (this.form.assetId != null) {\n        getApplicationInfo(this.form.assetId).then(response => {\n          console.log(response.data,'data')\n          this.form = response.data;\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n        });\n      }\n    },\n\n    // 校验数据\n    validateForm() {\n      let flag = null\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          flag = true\n        } else {\n          flag = false\n        }\n      })\n      return flag\n    },\n\n    /** 保存按钮操作 */\n    handleSave() {\n      console.log(this.form,'ffff')\n      return new Promise((resolve, reject) => {\n        this.$refs[\"form\"].validate(valid => {\n          let link = this.filterNull(this.form.links)\n          link.forEach(l => {\n            if (!(l.linkIp && l.linkIp.length > 0)) {\n              l.linkIp = null;\n            }\n            if (!(l.linkPort && l.linkPort.length > 0)) {\n              l.linkPort = null;\n            }\n          })\n          if (valid) {\n            this.form.links = link;\n            if (this.form.assetId != null) {\n              updateApplicationInfo(this.form).then(response => {\n                this.$modal.msgSuccess(\"修改成功\");\n                this.init()\n                return resolve();\n              }).catch((err) => {\n                return reject(err);\n              });\n            } else {\n              addApplicationInfo(this.form).then(response => {\n                this.form.assetId = response.data;\n                this.changeId(response.data);\n                this.$modal.msgSuccess(\"新增成功\");\n                this.init()\n                return resolve();\n              }).catch(err => {\n                return reject(err);\n              });\n            }\n          } else {\n            this.$modal.msgWarning(\"参数校验失败，请输入正确的参数！\");\n            return reject();\n          }\n        });\n      })\n    },\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        waitingInsuranceFilingNumber: undefined,\n        waitingInsuranceFilingTime: undefined,\n        waitingInsuranceFilingScan: undefined,\n        productName: undefined,\n        versionNumber: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.resetForm(\"form\");\n    },\n    /** 用户选择 */\n    handleUserSelect(val) {\n      if (this.form.manager == null || this.form.manager == '') {\n        this.userdata = null;\n      } else {\n        this.userdata = '1';\n      }\n      this.form.phone = val;\n    },\n    /** 过滤空值 */\n    filterNull(value) {\n      // return value.filter((item) => JSON.stringify(item) !== '{}');\n      return value.filter((item) => Object.keys(item).length !== 0);\n    },\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.el-divider {\n  background: #0E94EA;\n}\n\n.el-divider--vertical {\n  display: inline-block;\n  width: 5px;\n  height: 2em;\n  margin: 0 8px 0 0;\n  //vertical-align: middle;\n  //position: relative;\n}\n\n.hr.el-divider {\n  margin: 0;\n  background: #DCDFE6;\n  border: 1px solid #DCDFE6;\n}\n\n.el-date-editor.el-input {\n  width: 100%;\n}\n\n.my-title {\n  display: inline-block;\n  vertical-align: center;\n}\n</style>\n"]}]}
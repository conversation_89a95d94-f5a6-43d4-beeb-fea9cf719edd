2025-08-12 00:00:00.157 [quartzScheduler_Worker-3] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.HostVulnScan.scan(HostVulnScan.java:61)
	... 10 common frames omitted
2025-08-12 00:00:00.170 [quartzScheduler_Worker-5] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.WebVulnScan.scan(WebVulnScan.java:54)
	... 10 common frames omitted
2025-08-12 00:00:00.192 [quartzScheduler_Worker-4] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.WebVulnScan.scan(WebVulnScan.java:54)
	... 10 common frames omitted
2025-08-12 00:00:00.192 [quartzScheduler_Worker-1] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
	at com.ruoyi.ffsafe.scantaskapi.event.HostVulnScan.scan(HostVulnScan.java:61)
	... 10 common frames omitted
2025-08-12 00:00:00.347 [quartzScheduler_Worker-7] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 创建web扫描任务失败!
	at com.ruoyi.ffsafe.scantaskapi.event.WebVulnScan.scan(WebVulnScan.java:64)
	... 10 common frames omitted
2025-08-12 00:00:00.518 [quartzScheduler_Worker-3] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 创建web扫描任务失败!
	at com.ruoyi.ffsafe.scantaskapi.event.WebVulnScan.scan(WebVulnScan.java:64)
	... 10 common frames omitted
2025-08-12 00:00:01.222 [quartzScheduler_Worker-5] ERROR c.r.q.u.AbstractQuartzJob - [execute,53] - 任务执行异常  - ：
java.lang.reflect.InvocationTargetException: null
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:56)
	at com.ruoyi.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)
	at com.ruoyi.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)
	at com.ruoyi.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:47)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: java.lang.Exception: 创建web扫描任务失败!
	at com.ruoyi.ffsafe.scantaskapi.event.WebVulnScan.scan(WebVulnScan.java:64)
	... 10 common frames omitted
2025-08-12 00:00:05.095 [taskScheduler-35] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getFirewallList,458] - 图幻-----获取防火墙列表-----失败： SSLHandshakeException: Remote host terminated the handshake
2025-08-12 00:06:34.026 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2598 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",64515,"*************",6379,"tcp","[1300018] 疑似 Redis 6379 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300018,2,"请求","","[]","2025-08-12 00:01:33"]
2025-08-12 00:29:03.801 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1717 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 00:28:52","/v2/flow-bypass-filtering-log"]
2025-08-12 00:30:05.006 [taskScheduler-26] ERROR c.r.s.s.i.TblFirewallPolicyServiceImpl - [getNatList,492] - 图幻-----获取防火墙NAT列表--失败：SSLHandshakeException: Remote host terminated the handshake
2025-08-12 00:35:35.461 [pool-255-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1091 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 00:35:08"]
2025-08-12 00:36:36.439 [pool-256-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1268 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 00:36:00"]
2025-08-12 00:51:30.906 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5435 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 00:51:15","/v2/flow-bypass-filtering-log"]
2025-08-12 01:00:34.593 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1192 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",62709,"**************",3306,"tcp","[2002842] mysql数据库root用户登录尝试","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiMDEiLCAiY29udGVudF9zdHJpbmciOiAiXHUwMDAxIn0sIHsiY29udGVudF9oZXgiOiAiNzI2Z...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2002842,2,"请求","+AAAAY+iOwH///8AIQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcm9vdAAUadHt+Uqe7Njs7YEoUxK7VF+RDjNwcm9kdWN0X3Bvc...","[]","2025-08-12 00:55:29"]
2025-08-12 01:13:59.158 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3875 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 01:13:45","/v2/flow-bypass-filtering-log"]
2025-08-12 01:30:35.683 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1102 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",32480,"**************",1521,"tcp","[1300006] 疑似 OracleDB 1521 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300006,2,"请求","","[]","2025-08-12 01:25:35"]
2025-08-12 02:21:18.078 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2424 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 02:21:05","/v2/flow-bypass-filtering-log"]
2025-08-12 02:43:43.067 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5367 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",47471,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","syMBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-12 02:38:39"]
2025-08-12 03:05:35.527 [pool-408-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1185 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 03:05:09"]
2025-08-12 03:06:36.522 [pool-409-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1259 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 03:06:20"]
2025-08-12 03:07:37.595 [pool-410-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1162 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 03:07:13"]
2025-08-12 03:08:38.938 [pool-411-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1246 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 03:08:04"]
2025-08-12 03:09:39.714 [pool-412-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1080 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 03:08:46"]
2025-08-12 03:28:37.308 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10088 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 03:28:17","/v2/flow-bypass-filtering-log"]
2025-08-12 03:51:04.132 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7932 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 03:50:45","/v2/flow-bypass-filtering-log"]
2025-08-12 04:35:53.582 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3038 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 04:35:40","/v2/flow-bypass-filtering-log"]
2025-08-12 04:45:35.176 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1257 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",54809,"**************",21,"tcp","[1300019] 疑似 FTP 21 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300019,2,"请求","","[]","2025-08-12 04:40:39"]
2025-08-12 05:20:42.634 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1560 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 05:20:31","/v2/flow-bypass-filtering-log"]
2025-08-12 05:28:38.575 [pool-553-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1339 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:27:48"]
2025-08-12 05:29:39.483 [pool-554-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1423 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:29:19"]
2025-08-12 05:32:32.712 [pool-557-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1406 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:32:07"]
2025-08-12 05:33:33.465 [pool-558-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1404 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:32:58"]
2025-08-12 05:35:35.275 [pool-560-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1180 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:35:21"]
2025-08-12 05:43:08.008 [Thread-7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4243 millis. update ffsafe_interface_config
         SET interface_name = ?,
            interface_cn_name = ?,
            interface_path = ?,
            
            interface_method = ?,
            data_last_time = ? 
        where id = ?["flowRiskAssets","流量风险资产","/v2/flow-risk-assets",1,"2025-08-12 05:41:03",3]
2025-08-12 05:49:31.526 [pool-575-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1355 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:49:08"]
2025-08-12 05:50:32.503 [pool-576-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1449 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:50:10"]
2025-08-12 05:51:34.067 [pool-577-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1237 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:51:09"]
2025-08-12 05:52:34.991 [pool-578-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1303 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:52:01"]
2025-08-12 05:53:36.154 [pool-579-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1400 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:52:52"]
2025-08-12 05:54:36.723 [pool-580-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1178 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:54:18"]
2025-08-12 05:55:37.679 [pool-581-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1046 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 05:55:09"]
2025-08-12 06:00:34.556 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1244 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",51045,"**************",21,"tcp","[1300019] 疑似 FTP 21 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300019,2,"请求","","[]","2025-08-12 05:56:09"]
2025-08-12 06:05:33.025 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2533 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 06:05:20","/v2/flow-bypass-filtering-log"]
2025-08-12 06:05:33.141 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3301 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",35663,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","ZTMBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-12 06:00:39"]
2025-08-12 06:05:35.751 [pool-591-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1382 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 06:05:26"]
2025-08-12 06:08:38.167 [pool-594-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1270 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 06:08:21"]
2025-08-12 06:27:38.795 [pool-613-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1682 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 06:27:23"]
2025-08-12 06:28:39.983 [pool-614-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1445 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 06:28:17"]
2025-08-12 06:45:38.623 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1606 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",42459,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","iUwBAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAAcAAE=","[]","2025-08-12 06:40:36"]
2025-08-12 06:50:23.053 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1483 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 06:50:11","/v2/flow-bypass-filtering-log"]
2025-08-12 07:00:38.964 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2131 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",59210,"**************",3306,"tcp","[2002842] mysql数据库root用户登录尝试","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiMDEiLCAiY29udGVudF9zdHJpbmciOiAiXHUwMDAxIn0sIHsiY29udGVudF9oZXgiOiAiNzI2Z...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2002842,2,"请求","+AAAAY+iOwH///8AIQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcm9vdAAU1f0MKEv5MiM4oB+5VXu8xVx2dJRwcm9kdWN0X3Bvc...","[]","2025-08-12 06:55:31"]
2025-08-12 07:30:36.328 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1452 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",24951,"**************",3306,"tcp","[2002842] mysql数据库root用户登录尝试","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiMDEiLCAiY29udGVudF9zdHJpbmciOiAiXHUwMDAxIn0sIHsiY29udGVudF9oZXgiOiAiNzI2Z...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2002842,2,"请求","+AAAAY+iOwH///8AIQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcm9vdAAUC3fkb2e2W+iW1wRKka1QvrwZv7Jwcm9kdWN0X3Bvc...","[]","2025-08-12 07:25:30"]
2025-08-12 07:35:17.968 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2595 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 07:35:05","/v2/flow-bypass-filtering-log"]
2025-08-12 07:45:34.665 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2643 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",34414,"**************",3306,"tcp","[2002842] mysql数据库root用户登录尝试","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiMDEiLCAiY29udGVudF9zdHJpbmciOiAiXHUwMDAxIn0sIHsiY29udGVudF9oZXgiOiAiNzI2Z...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2002842,2,"请求","XQAAAQ2iCwABAAAAIQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcm9vdAAUCPjyTvWvAwNC8pOR3nK0OL3yZEl5bWNkX3ZsYWRkZ...","[]","2025-08-12 07:40:30"]
2025-08-12 07:45:37.654 [pool-694-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1444 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 07:45:18"]
2025-08-12 07:46:38.384 [pool-695-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1355 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 07:46:10"]
2025-08-12 07:57:43.087 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5718 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 07:57:27","/v2/flow-bypass-filtering-log"]
2025-08-12 08:42:32.508 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1733 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 08:42:20","/v2/flow-bypass-filtering-log"]
2025-08-12 09:04:58.040 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5277 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 09:04:42","/v2/flow-bypass-filtering-log"]
2025-08-12 09:15:35.487 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2555 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",49820,"**************",3306,"tcp","[2002842] mysql数据库root用户登录尝试","异常行为/访问异常","W3siY29udGVudF9oZXgiOiAiMDEiLCAiY29udGVudF9zdHJpbmciOiAiXHUwMDAxIn0sIHsiY29udGVudF9oZXgiOiAiNzI2Z...","af1eda48-6135-11ef-8e67-000c29677ec8","********",2002842,2,"请求","+AAAAY+iOwH///8AIQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcm9vdAAUyOkIrm9bqf8AKNi0DM0JH/aMzfhwcm9kdWN0X3Bvc...","[]","2025-08-12 09:10:31"]
2025-08-12 09:27:24.168 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3983 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 09:27:10","/v2/flow-bypass-filtering-log"]
2025-08-12 09:30:35.739 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2018 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",64816,"**************",1521,"tcp","[1300006] 疑似 OracleDB 1521 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300006,2,"请求","","[]","2025-08-12 09:25:30"]
2025-08-12 09:45:36.620 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1998 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",19749,"**************",1521,"tcp","[1300006] 疑似 OracleDB 1521 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300006,2,"请求","","[]","2025-08-12 09:40:32"]
2025-08-12 09:49:05.017 [hutool-cron-1] ERROR c.h.c.l.TaskListenerManager - [notifyTaskFailed,86] - 创建流程失败:系统异常
com.ruoyi.common.exception.ServiceException: 创建流程失败:系统异常
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.createFlowTask(TblOperateWorkServiceImpl.java:333)
	at com.ruoyi.monitor2.service.impl.TblOperateWorkServiceImpl.lambda$1(TblOperateWorkServiceImpl.java:221)
	at cn.hutool.cron.task.CronTask.execute(CronTask.java:31)
	at cn.hutool.cron.TaskExecutor.run(TaskExecutor.java:52)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-12 09:49:50.684 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4917 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 09:49:35","/v2/flow-bypass-filtering-log"]
2025-08-12 09:59:32.165 [pool-830-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1150 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 09:59:14"]
2025-08-12 10:00:38.457 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1743 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",27743,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-12 09:55:30"]
2025-08-12 10:04:35.175 [pool-835-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1070 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 10:04:24"]
2025-08-12 10:05:36.220 [pool-836-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1277 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 10:05:15"]
2025-08-12 10:06:37.436 [pool-837-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1292 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 10:06:06"]
2025-08-12 10:07:38.154 [pool-838-thread-2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1437 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:13","2025-08-12 10:07:18"]
2025-08-12 10:07:38.323 [pool-838-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1481 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-11 08:00:01","2025-08-12 10:06:58"]
2025-08-12 10:07:38.553 [pool-838-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1731 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:02","2025-08-12 10:07:28"]
2025-08-12 10:07:39.996 [pool-838-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1668 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 10:06:58"]
2025-08-12 10:10:33.418 [pool-841-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1715 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 10:10:27"]
2025-08-12 10:11:32.873 [pool-842-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1177 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:02","2025-08-12 10:11:15"]
2025-08-12 10:11:33.869 [pool-842-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1355 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 10:11:19"]
2025-08-12 10:12:21.366 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11223 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-12 10:12:00","/v2/flow-bypass-filtering-log"]
2025-08-12 10:12:34.836 [pool-843-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1208 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 10:12:11"]
2025-08-12 10:15:38.668 [Thread-5] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1136 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ? )["**************",36880,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-12 10:10:36"]
2025-08-12 10:16:31.981 [pool-847-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1064 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-11 08:00:01","2025-08-12 10:15:56"]
2025-08-12 10:18:21.572 [Thread-10] ERROR c.r.d.e.InterfaceEventMonitor - [run,349] - InterfaceEventMonitor thread error: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Aug 12 10:18:21 CST 2025
### The error may exist in file [E:\wsh\augment_workspace\aqsoc-main\aqsoc-monitor\target\classes\mapper\datainterface\DataDictChangeMapper.xml]
### The error may involve com.ruoyi.datainterface.mapper.DataDictChangeMapper.selectDataDictChangeList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.DataSourceClosedException: dataSource already closed at Tue Aug 12 10:18:21 CST 2025

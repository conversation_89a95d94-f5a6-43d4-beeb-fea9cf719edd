{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\networkdevices\\networkDeviceDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\networkdevices\\networkDeviceDetail.vue", "mtime": 1754969114156}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["networkDeviceDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "networkDeviceDetail.vue", "sourceRoot": "src/views/safe/networkdevices", "sourcesContent": ["<!-- 网络、安全设备详情 -->\n<template>\n  <el-dialog :visible.sync=\"visible\" :title=\"assetName\" width=\"50%\">\n    <div class=\"customForm-container\" style=\"padding: 0 20px\">\n      <div class=\"my-title\"><img src=\"@/assets/images/application/jbxx.png\" alt=\"\">基本信息</div>\n      <el-descriptions\n        class=\"custom-column\"\n        direction=\"vertical\"\n        size=\"medium\"\n        :colon=\"false\"\n        label-class-name=\"custom-label-style\"\n        content-class-name=\"custom-content-style\"\n        :column=\"3\"\n      >\n        <template v-for=\"item in visibleAssetFields\">\n          <el-descriptions-item :key=\"item.fieldKey\" :label=\"item.fieldName\">\n            <!-- 字典字段特殊处理 -->\n            <template v-if=\"dictFields.includes(item.fieldKey)\">\n              <span\n                v-for=\"dict in getDictData(item.fieldKey)\"\n                :key=\"dict.value\"\n                v-if=\"dict.value === asset[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <template v-if=\"['deptId', 'manageDeptId'].includes(item.fieldKey)\">\n              <span\n                v-for=\"dict in deptOptions\"\n                :key=\"dict.id\"\n                v-if=\"dict.id === asset[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <!-- 普通字段直接显示 -->\n            <template v-else>\n              {{ getFieldValue(item.fieldKey) }}\n            </template>\n          </el-descriptions-item>\n        </template>\n      </el-descriptions>\n\n      <div class=\"my-title\" style=\"margin: 20px 0\"><img src=\"@/assets/images/application/netinfo.png\" alt=\"\">网络信息\n      </div>\n      <el-table v-loading=\"loadingIpMac\" :data=\"ipMacList\">\n        <el-table-column label=\"所属网络\" prop=\"domainFullName\" show-overflow-tooltip/>\n        <el-table-column label=\"ipv4/ipv6\" prop=\"ipv4\"/>\n        <el-table-column label=\"MAC\" prop=\"mac\"/>\n        <el-table-column label=\"网关\" prop=\"defaultGateway\"/>\n        <el-table-column label=\"备注\" prop=\"remark\" show-overflow-tooltip/>\n      </el-table>\n      <pagination\n        v-show=\"totalIpMac>=0\"\n        :total=\"totalIpMac\"\n        :page.sync=\"queryParamsIpMac.pageNum\"\n        :limit.sync=\"queryParamsIpMac.pageSize\"\n        @pagination=\"getIpMacList\"\n      />\n    </div>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"visible = false\">取 消</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {getAssetInfo} from \"@/api/safe/overview\";\nimport {listMacAip} from \"@/api/safe/macAip\";\nimport assetRegister from \"@/mixins/assetRegister\";\nimport {getAllDeptTree} from \"@/api/system/user\";\n\nexport default {\n  mixins: [assetRegister],\n  name: \"networkDeviceDetail\",\n  props: {\n    assetId: {\n      type: String,\n      default: null\n    },\n    assetName: {\n      type: String,\n      default: null\n    },\n    deviceDetailVisible: {\n      type: Boolean,\n      default: false\n    },\n    assetAllocationType: {\n      type: String,\n      default: '4'\n    }\n  },\n  dicts: ['sys_yes_no', 'impt_grade', 'is_sparing'], // 保留字典配置\n  data() {\n    return {\n      asset: {},\n      queryParamsIpMac: {\n        pageNum: 1,\n        pageSize: 10,\n        assetId: null,\n      },\n      ipMacList: [],\n      totalIpMac: 0,\n      loadingIpMac: false,\n      deptOptions: [],\n    };\n  },\n  watch: {\n    assetId(newVal) {\n      if (newVal) {\n        this.getAsset();\n        this.getIpMacList();\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.deviceDetailVisible;\n      },\n      set(val) {\n        this.$emit(\"update:deviceDetailVisible\", val);\n      }\n    },\n    visibleAssetFields() {\n      // 获取资产字段列表\n      const assetListData = this.assetList && this.assetList.length ? this.assetList[0].fieldsItems : []\n      return assetListData.filter(item => item.isShow);\n    },\n\n    // 需要字典转换的字段\n    dictFields() {\n      return ['isVirtual', 'degreeImportance', 'isSparing'];\n    }\n  },\n  created() {\n    this.getDeptTree();\n  },\n  methods: {\n    getDictData(fieldKey) {\n      switch (fieldKey) {\n        case 'isVirtual':\n          return this.dict.type.sys_yes_no;\n        case 'degreeImportance':\n          return this.dict.type.impt_grade;\n          case 'isSparing':\n            return this.dict.type.is_sparing;\n        default:\n          return [];\n      }\n    },\n\n    /** 查询所属部门 */\n    getDeptTree() {\n      getAllDeptTree().then(response => {\n        // 将部门树结构转换为平级结构\n        const flattenDeptTree = (nodes) => {\n          let result = [];\n          nodes.forEach(node => {\n            result.push({\n              id: node.id,\n              label: node.label\n            });\n            if (node.children && node.children.length > 0) {\n              result = result.concat(flattenDeptTree(node.children));\n            }\n          });\n          return result;\n        };\n\n        this.deptOptions = flattenDeptTree(response.data);\n      });\n    },\n\n    getAsset() {\n      getAssetInfo(this.assetId).then(response => {\n        this.asset = response.data || {};\n      });\n    },\n    getIpMacList() {\n      this.loadingIpMac = true;\n      this.queryParamsIpMac.assetId = this.assetId;\n      listMacAip(this.queryParamsIpMac).then(response => {\n        this.ipMacList = response.rows;\n        this.totalIpMac = response.total;\n        this.loadingIpMac = false;\n      });\n    },\n    getFieldValue(field) {\n      if(this.dictFields.includes(field)){\n        return '';\n      }\n      const value = this.asset[field];\n      if (field === 'maintainUnit') {\n        return this.asset.maintainUnitName;\n      }\n      if (field === 'facilityManufacturer') {\n        return this.asset.facilityManufacturerName || value;\n      }\n      if (field === 'vendor') {\n        return this.asset.vendorName || value;\n      }\n      if (field === 'assetType') {\n        return this.asset.assetTypeDesc || value;\n      }\n      if (field === 'domainId') {\n        return this.asset.domainName || value;\n      }\n      if (field === 'locationId') {\n        return this.asset.locationFullName || value;\n      }\n      return value;\n    },\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n::v-deep .el-dialog__body {\n  padding: 0 0 20px 0 !important;\n}\n</style>\n"]}]}
{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\basicInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\basicInfo.vue", "mtime": 1754969114240}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_assetRegister", "_interopRequireDefault", "require", "name", "mixins", "assetRegister", "dicts", "props", "form", "type", "Object", "default", "edrForm", "myTags", "Array", "macAipList", "data", "activeNames", "collapseLabelSpan", "collapseContentSpan", "headerCellStyle", "color", "cellStyle", "assetAllocationType", "computed", "visibleAssetFields", "assetList", "map", "group", "_objectSpread2", "fieldsItems", "filter", "item", "isShow", "mounted", "methods", "getFieldValue", "field", "<PERSON><PERSON><PERSON>", "getFieldDisplayValue", "value", "_this$dict$type$impt_", "dict", "impt_grade", "find", "d", "label", "_this$dict$type$is_sp", "is_sparing", "maintainUnitName", "facilityManufacturerName", "vendorName", "assetTypeDesc"], "sources": ["src/views/safe/server/basicInfo.vue"], "sourcesContent": ["<template>\n  <div class=\"customForm-container\" style=\"height: 100%; overflow: hidden\">\n    <template v-for=\"group in visibleAssetFields\">\n      <div :key=\"group.formName\">\n        <div class=\"my-title\">\n          <!-- 动态图标渲染 -->\n          <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/jbxx.png\" alt=\"\"/>\n          <i v-else-if=\"group.formName === '硬件/软件概况信息'\" class=\"el-icon-cpu\" style=\"font-size: 24px; margin: 0 5px; color: #4382fd\" />\n          <i v-else-if=\"group.formName === '位置信息'\" class=\"el-icon-location-information\" style=\"font-size: 24px; margin: 0 5px; color: #4382fd\" />\n          <img v-else-if=\"group.formName === '网络信息'\" src=\"@/assets/images/application/netinfo.png\" alt=\"\"/>\n          {{ group.formName }}\n        </div>\n\n        <!-- 网络信息特殊处理 -->\n        <template v-if=\"group.formName === '网络信息'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item label=\"外网IP\">\n              <el-col :class=\"[ !form.exposedIp ? 'empty' : 'collapse-value' ]\">\n                {{ form.exposedIp || '（空）' }}\n              </el-col>\n            </el-descriptions-item>\n          </el-descriptions>\n          <el-table :data=\"macAipList\" :header-cell-style=\"headerCellStyle\" :cell-style=\"cellStyle\">\n            <el-table-column label=\"是否主ip\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.mainIp === '1'\">是</span>\n                <span v-if=\"scope.row.mainIp === '0'\">否</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"domainFullName\" label=\"所属网络\" />\n            <el-table-column prop=\"ipv4\" label=\"ip\" />\n            <el-table-column prop=\"mac\" label=\"mac\" />\n          </el-table>\n        </template>\n\n        <!-- 其他分组正常渲染 -->\n        <template v-else>\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\">\n              <!-- 所属部门特殊处理 -->\n              <template v-if=\"field.fieldKey === 'deptId'\">\n                <span :class=\"[ !form.deptName ? 'empty' : 'collapse-value' ]\">\n                  {{ form.deptName || '（空）' }}\n                </span>\n              </template>\n\n              <!-- 其他字段正常渲染 -->\n              <template v-else>\n                <span :class=\"[ !getFieldValue(field) ? 'empty' : 'collapse-value' ]\">\n                  {{ getFieldDisplayValue(field) || '（空）' }}\n                </span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport assetRegister from \"@/mixins/assetRegister\";\nexport default {\n  name: 'BasicInfo',\n  mixins: [assetRegister],\n  dicts: ['impt_grade', 'sys_yes_no', 'proc_type','is_sparing'],\n  props: {\n    form: {\n      type: Object,\n      default: null\n    },\n    edrForm: {\n      type: Object,\n      default: null\n    },\n    myTags: {\n      type: Array,\n      default: null\n    },\n    macAipList: {\n      type: Array,\n      default: null\n    }\n  },\n  data() {\n    return {\n      activeNames: ['1', '2', '3', '4'],\n      collapseLabelSpan: 4,\n      collapseContentSpan: 8,\n      headerCellStyle: { 'font-weight': 'normal', color: '#979797' },\n      cellStyle: { 'font-weight': 'bold' },\n      assetAllocationType: '2',\n    }\n  },\n  computed: {\n    visibleAssetFields() {\n      return (this.assetList || []).map(group => ({\n        ...group,\n        fieldsItems: group.fieldsItems.filter(item => item.isShow)\n      }));\n    }\n  },\n  mounted() {},\n  methods: {\n    getFieldValue(field) {\n      return this.form[field.fieldKey];\n    },\n    getFieldDisplayValue(field) {\n      const value = this.getFieldValue(field);\n      // 特殊字段处理\n      if (field.fieldKey === 'degreeImportance') {\n        return this.dict.type.impt_grade.find(d => d.value === value)?.label || value;\n      }\n      if (field.fieldKey === 'isVirtual') {\n        return value === 'Y' ? '是' : value === 'N' ? '否' : value;\n      }\n      if (field.fieldKey === 'isSparing') {\n        return this.dict.type.is_sparing.find(d => d.value === value)?.label || value;\n      }\n      if (field.fieldKey === 'maintainUnit') {\n        return this.form.maintainUnitName;\n      }\n      if (field.fieldKey === 'facilityManufacturer') {\n        return this.form.facilityManufacturerName || value;\n      }\n      if (field.fieldKey === 'vendor') {\n        return this.form.vendorName || value;\n      }\n      if (field.fieldKey === 'assetType') {\n        return this.form.assetTypeDesc || value;\n      }\n      return value;\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n  .collapse-title {\n    font-weight: bold;\n    margin-left: 20px;\n  }\n  .collapse-row {\n    line-height: 32px;\n  }\n  .collapse-content-div {\n    margin: 0 20px;\n  }\n  .collapse-label {\n    color: #979797;\n  }\n  .collapse-value {\n    font-weight: bold;\n  }\n  .empty {\n    color: #979797;\n  }\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;AA8EA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,MAAA,GAAAC,sBAAA;EACAC,KAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,OAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,MAAA;MACAJ,IAAA,EAAAK,KAAA;MACAH,OAAA;IACA;IACAI,UAAA;MACAN,IAAA,EAAAK,KAAA;MACAH,OAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,iBAAA;MACAC,mBAAA;MACAC,eAAA;QAAA;QAAAC,KAAA;MAAA;MACAC,SAAA;QAAA;MAAA;MACAC,mBAAA;IACA;EACA;EACAC,QAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA,aAAAC,SAAA,QAAAC,GAAA,WAAAC,KAAA;QAAA,WAAAC,cAAA,CAAAlB,OAAA,MAAAkB,cAAA,CAAAlB,OAAA,MACAiB,KAAA;UACAE,WAAA,EAAAF,KAAA,CAAAE,WAAA,CAAAC,MAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,MAAA;UAAA;QAAA;MAAA,CACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAAC,KAAA;MACA,YAAA7B,IAAA,CAAA6B,KAAA,CAAAC,QAAA;IACA;IACAC,oBAAA,WAAAA,qBAAAF,KAAA;MACA,IAAAG,KAAA,QAAAJ,aAAA,CAAAC,KAAA;MACA;MACA,IAAAA,KAAA,CAAAC,QAAA;QAAA,IAAAG,qBAAA;QACA,SAAAA,qBAAA,QAAAC,IAAA,CAAAjC,IAAA,CAAAkC,UAAA,CAAAC,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAL,KAAA,KAAAA,KAAA;QAAA,gBAAAC,qBAAA,uBAAAA,qBAAA,CAAAK,KAAA,KAAAN,KAAA;MACA;MACA,IAAAH,KAAA,CAAAC,QAAA;QACA,OAAAE,KAAA,iBAAAA,KAAA,iBAAAA,KAAA;MACA;MACA,IAAAH,KAAA,CAAAC,QAAA;QAAA,IAAAS,qBAAA;QACA,SAAAA,qBAAA,QAAAL,IAAA,CAAAjC,IAAA,CAAAuC,UAAA,CAAAJ,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAL,KAAA,KAAAA,KAAA;QAAA,gBAAAO,qBAAA,uBAAAA,qBAAA,CAAAD,KAAA,KAAAN,KAAA;MACA;MACA,IAAAH,KAAA,CAAAC,QAAA;QACA,YAAA9B,IAAA,CAAAyC,gBAAA;MACA;MACA,IAAAZ,KAAA,CAAAC,QAAA;QACA,YAAA9B,IAAA,CAAA0C,wBAAA,IAAAV,KAAA;MACA;MACA,IAAAH,KAAA,CAAAC,QAAA;QACA,YAAA9B,IAAA,CAAA2C,UAAA,IAAAX,KAAA;MACA;MACA,IAAAH,KAAA,CAAAC,QAAA;QACA,YAAA9B,IAAA,CAAA4C,aAAA,IAAAZ,KAAA;MACA;MACA,OAAAA,KAAA;IACA;EACA;AACA", "ignoreList": []}]}
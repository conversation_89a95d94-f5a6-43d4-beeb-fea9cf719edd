{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\networkdevices\\networkDeviceDetail.vue?vue&type=style&index=0&id=52f7798d&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\networkdevices\\networkDeviceDetail.vue", "mtime": 1754969114156}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICJAL2Fzc2V0cy9zdHlsZXMvY3VzdG9tRm9ybSI7Cjo6di1kZWVwIC5lbC1kaWFsb2dfX2JvZHkgewogIHBhZGRpbmc6IDAgMCAyMHB4IDAgIWltcG9ydGFudDsKfQo="}, {"version": 3, "sources": ["networkDeviceDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6NA;AACA;AACA;AACA", "file": "networkDeviceDetail.vue", "sourceRoot": "src/views/safe/networkdevices", "sourcesContent": ["<!-- 网络、安全设备详情 -->\n<template>\n  <el-dialog :visible.sync=\"visible\" :title=\"assetName\" width=\"50%\">\n    <div class=\"customForm-container\" style=\"padding: 0 20px\">\n      <div class=\"my-title\"><img src=\"@/assets/images/application/jbxx.png\" alt=\"\">基本信息</div>\n      <el-descriptions\n        class=\"custom-column\"\n        direction=\"vertical\"\n        size=\"medium\"\n        :colon=\"false\"\n        label-class-name=\"custom-label-style\"\n        content-class-name=\"custom-content-style\"\n        :column=\"3\"\n      >\n        <template v-for=\"item in visibleAssetFields\">\n          <el-descriptions-item :key=\"item.fieldKey\" :label=\"item.fieldName\">\n            <!-- 字典字段特殊处理 -->\n            <template v-if=\"dictFields.includes(item.fieldKey)\">\n              <span\n                v-for=\"dict in getDictData(item.fieldKey)\"\n                :key=\"dict.value\"\n                v-if=\"dict.value === asset[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <template v-if=\"['deptId', 'manageDeptId'].includes(item.fieldKey)\">\n              <span\n                v-for=\"dict in deptOptions\"\n                :key=\"dict.id\"\n                v-if=\"dict.id === asset[item.fieldKey]\"\n              >\n                {{ dict.label }}\n              </span>\n            </template>\n\n            <!-- 普通字段直接显示 -->\n            <template v-else>\n              {{ getFieldValue(item.fieldKey) }}\n            </template>\n          </el-descriptions-item>\n        </template>\n      </el-descriptions>\n\n      <div class=\"my-title\" style=\"margin: 20px 0\"><img src=\"@/assets/images/application/netinfo.png\" alt=\"\">网络信息\n      </div>\n      <el-table v-loading=\"loadingIpMac\" :data=\"ipMacList\">\n        <el-table-column label=\"所属网络\" prop=\"domainFullName\" show-overflow-tooltip/>\n        <el-table-column label=\"ipv4/ipv6\" prop=\"ipv4\"/>\n        <el-table-column label=\"MAC\" prop=\"mac\"/>\n        <el-table-column label=\"网关\" prop=\"defaultGateway\"/>\n        <el-table-column label=\"备注\" prop=\"remark\" show-overflow-tooltip/>\n      </el-table>\n      <pagination\n        v-show=\"totalIpMac>=0\"\n        :total=\"totalIpMac\"\n        :page.sync=\"queryParamsIpMac.pageNum\"\n        :limit.sync=\"queryParamsIpMac.pageSize\"\n        @pagination=\"getIpMacList\"\n      />\n    </div>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"visible = false\">取 消</el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport {getAssetInfo} from \"@/api/safe/overview\";\nimport {listMacAip} from \"@/api/safe/macAip\";\nimport assetRegister from \"@/mixins/assetRegister\";\nimport {getAllDeptTree} from \"@/api/system/user\";\n\nexport default {\n  mixins: [assetRegister],\n  name: \"networkDeviceDetail\",\n  props: {\n    assetId: {\n      type: String,\n      default: null\n    },\n    assetName: {\n      type: String,\n      default: null\n    },\n    deviceDetailVisible: {\n      type: Boolean,\n      default: false\n    },\n    assetAllocationType: {\n      type: String,\n      default: '4'\n    }\n  },\n  dicts: ['sys_yes_no', 'impt_grade', 'is_sparing'], // 保留字典配置\n  data() {\n    return {\n      asset: {},\n      queryParamsIpMac: {\n        pageNum: 1,\n        pageSize: 10,\n        assetId: null,\n      },\n      ipMacList: [],\n      totalIpMac: 0,\n      loadingIpMac: false,\n      deptOptions: [],\n    };\n  },\n  watch: {\n    assetId(newVal) {\n      if (newVal) {\n        this.getAsset();\n        this.getIpMacList();\n      }\n    }\n  },\n  computed: {\n    visible: {\n      get() {\n        return this.deviceDetailVisible;\n      },\n      set(val) {\n        this.$emit(\"update:deviceDetailVisible\", val);\n      }\n    },\n    visibleAssetFields() {\n      // 获取资产字段列表\n      const assetListData = this.assetList && this.assetList.length ? this.assetList[0].fieldsItems : []\n      return assetListData.filter(item => item.isShow);\n    },\n\n    // 需要字典转换的字段\n    dictFields() {\n      return ['isVirtual', 'degreeImportance', 'isSparing'];\n    }\n  },\n  created() {\n    this.getDeptTree();\n  },\n  methods: {\n    getDictData(fieldKey) {\n      switch (fieldKey) {\n        case 'isVirtual':\n          return this.dict.type.sys_yes_no;\n        case 'degreeImportance':\n          return this.dict.type.impt_grade;\n          case 'isSparing':\n            return this.dict.type.is_sparing;\n        default:\n          return [];\n      }\n    },\n\n    /** 查询所属部门 */\n    getDeptTree() {\n      getAllDeptTree().then(response => {\n        // 将部门树结构转换为平级结构\n        const flattenDeptTree = (nodes) => {\n          let result = [];\n          nodes.forEach(node => {\n            result.push({\n              id: node.id,\n              label: node.label\n            });\n            if (node.children && node.children.length > 0) {\n              result = result.concat(flattenDeptTree(node.children));\n            }\n          });\n          return result;\n        };\n\n        this.deptOptions = flattenDeptTree(response.data);\n      });\n    },\n\n    getAsset() {\n      getAssetInfo(this.assetId).then(response => {\n        this.asset = response.data || {};\n      });\n    },\n    getIpMacList() {\n      this.loadingIpMac = true;\n      this.queryParamsIpMac.assetId = this.assetId;\n      listMacAip(this.queryParamsIpMac).then(response => {\n        this.ipMacList = response.rows;\n        this.totalIpMac = response.total;\n        this.loadingIpMac = false;\n      });\n    },\n    getFieldValue(field) {\n      if(this.dictFields.includes(field)){\n        return '';\n      }\n      const value = this.asset[field];\n      if (field === 'maintainUnit') {\n        return this.asset.maintainUnitName;\n      }\n      if (field === 'facilityManufacturer') {\n        return this.asset.facilityManufacturerName || value;\n      }\n      if (field === 'vendor') {\n        return this.asset.vendorName || value;\n      }\n      if (field === 'assetType') {\n        return this.asset.assetTypeDesc || value;\n      }\n      if (field === 'domainId') {\n        return this.asset.domainName || value;\n      }\n      if (field === 'locationId') {\n        return this.asset.locationFullName || value;\n      }\n      return value;\n    },\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n::v-deep .el-dialog__body {\n  padding: 0 0 20px 0 !important;\n}\n</style>\n"]}]}
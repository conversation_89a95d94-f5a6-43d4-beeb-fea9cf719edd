{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\systemDetails.vue?vue&type=style&index=0&id=16ec437e&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\hhlCode\\component\\systemDetails.vue", "mtime": 1754969114071}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICJAL2Fzc2V0cy9zdHlsZXMvY3VzdG9tRm9ybSI7Ci5ib3gtY29udGFpbmVyIHsKICBwYWRkaW5nLXJpZ2h0OiAyMHB4OwogIC5teS1mb3JtIHsKICAgIHdpZHRoOiAxMDAlOwogICAgbWFyZ2luOiAwIDEwcHg7CiAgfQp9Cjo6di1kZWVwIC5lbC1zZWxlY3QtZHJvcGRvd24gewogIHBvc2l0aW9uOiBhYnNvbHV0ZSAhaW1wb3J0YW50OwogIGxlZnQ6IDAgIWltcG9ydGFudDsKICB0b3A6IDMwcHghaW1wb3J0YW50Owp9Cjo6di1kZWVwIC5lbC1kYXRlLWVkaXRvciB7CiAgd2lkdGg6IDEwMCU7Cn0KCjo6di1kZWVwIC5hc3NvY2lhdGlvblNlcnZlcnsKICAuZWwtZm9ybS1pdGVtX19jb250ZW50ewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgfQogIC5lbC1mb3JtLWl0ZW1fX2xhYmVsewogICAgZGlzcGxheTogY29udGVudHM7CiAgfQp9Cg=="}, {"version": 3, "sources": ["systemDetails.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8oCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "systemDetails.vue", "sourceRoot": "src/views/hhlCode/component", "sourcesContent": ["<template>\n  <div class=\"box-container\" style=\"overflow-x: hidden;\" v-loading=\"loading\">\n    <!--系统基本信息-->\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" >\n      <el-row>\n        <el-col :span=\"24\">\n          <!-- 动态渲染字段 -->\n          <template v-for=\"group in basicInfoAssetFields\">\n            <el-row\n              :gutter=\"20\"\n              type=\"flex\"\n              :key=\"group.formName\"\n              v-if=\"group.isShow\"\n              style=\"flex-wrap: wrap;margin-bottom: 10px;\">\n              <el-col :span=\"24\">\n                <div class=\"my-title\" v-if=\"group.isShow\">\n                  <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/jbxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '备案信息'\" src=\"@/assets/images/application/baxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '测评信息'\" src=\"@/assets/images/application/cpxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '外部连接信息'\" src=\"@/assets/images/application/wblj.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '拓扑结构信息'\" src=\"@/assets/images/application/tpxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '运营维护情况'\" src=\"@/assets/images/application/ywxx.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '其他基本信息'\" src=\"@/assets/images/application/qtxx.png\" alt=\"\"/>\n                  {{ group.formName }}\n                </div>\n              </el-col>\n              <template v-if=\"group.formName === '外部连接信息'\">\n                <el-col :span=\"24\">\n                  <ApplicationLink\n                    v-if=\"group.isShow\"\n                    :fields=\"group.fieldsItems\"\n                    :disabled=\"!$editable.value\"\n                    v-model=\"form.links\"/>\n                </el-col>\n              </template>\n\n              <template v-else-if=\"group.formName === '运营维护情况'\">\n                <el-col :span=\"24\">\n                  <ApplicationSite\n                    ref=\"site\"\n                    v-if=\"group.isShow\"\n                    :fields=\"group.fieldsItems\"\n                    :disabled=\"!$editable.value\"\n                    :value.sync=\"form.eids\"\n                    :asset-id=\"form.assetId\"\n                    multiple/>\n                </el-col>\n              </template>\n\n              <template v-else>\n                <el-col\n                  v-for=\"field in group.fieldsItems\"\n                  :key=\"field.fieldKey\"\n                  :style=\"radioGroupType.includes(field.fieldKey) ? { display: 'flex' } : ''\"\n                  :span=\"getFieldSpan(field)\"\n                  v-if=\"shouldShowField(field)\"\n                >\n                  <!-- 其他系统备注 -->\n                  <template v-if=\"field.fieldKey === 'otherSystemNotes'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"form.systemType == 12\"\n                    >\n                      <el-input\n                        v-model=\"form.otherSystemNotes\"\n                        placeholder=\"请输入其它系统备注\"\n                      />\n                    </el-form-item>\n                  </template>\n                  <!-- 其他基本信息：信创适配时间 -->\n                  <template v-else-if=\"field.fieldKey === 'adaptDate'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"form.isadapt === 'Y'\"\n                    >\n                      <el-date-picker\n                        clearable\n                        v-model=\"form.adaptDate\"\n                        type=\"date\"\n                        format=\"yyyy 年 MM 月 dd 日\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"请选择建设时间\">\n                      </el-date-picker>\n                    </el-form-item>\n                  </template>\n                  <!-- 其他基本信息：密码应用建设时间 -->\n                  <template v-else-if=\"field.fieldKey === 'cipherDate'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"form.iscipher === 'Y'\"\n                    >\n                      <el-date-picker\n                        clearable\n                        v-model=\"form.cipherDate\"\n                        type=\"date\"\n                        format=\"yyyy 年 MM 月 dd 日\"\n                        value-format=\"yyyy-MM-dd\"\n                        placeholder=\"请选择建设时间\">\n                      </el-date-picker>\n                    </el-form-item>\n                  </template>\n                  <!-- 其他基本信息：警综对接 -->\n                  <template v-else-if=\"field.fieldKey === 'islink'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      v-if=\"deployLocation === 'fair'\"\n                    >\n                      <el-radio-group v-model=\"form.islink\">\n                        <el-radio\n                          v-for=\"dict in dict.type.sys_yes_no\"\n                          :key=\"dict.value\"\n                          :label=\"dict.value\"\n                        >{{ dict.label }}\n                        </el-radio>\n                      </el-radio-group>\n                    </el-form-item>\n                  </template>\n                  <!-- 关联服务器字段处理 -->\n                  <template v-else-if=\"field.fieldKey === 'associationServer'\">\n                    <el-form-item\n                      :label=\"field.fieldName\"\n                      :prop=\"field.fieldKey\"\n                      :rules=\"getFieldRules(field)\"\n                      class=\"associationServer\"\n                    >\n                      <el-select\n                        v-model=\"form.associationServer\"\n                        placeholder=\"请选择服务器\"\n                        multiple\n                        filterable\n                        clearable\n                        @change=\"serverChange\"\n                      >\n                        <el-option\n                          v-for=\"server in serverOptions\"\n                          :key=\"server.assetId\"\n                          :label=\"server.assetName\"\n                          :value=\"server.assetId\"\n                        >\n                          <span style=\"float: left\">{{ server.assetName }}</span>\n                          <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ server.ip }}</span>\n                        </el-option>\n                      </el-select>\n                      <el-button\n                        size=\"mini\"\n                        plain\n                        type=\"primary\"\n                        style=\"margin-left: 10px;\"\n                        @click=\"addAssetHandle\"\n                        :disabled=\"!$editable.value\"\n                      >\n                        新增服务器\n                      </el-button>\n                    </el-form-item>\n                  </template>\n\n                  <el-form-item\n                    v-else\n                    :label=\"field.fieldName\"\n                    :prop=\"field.fieldKey\"\n                    :rules=\"getFieldRules(field)\"\n                  >\n                    <template v-if=\"field.fieldKey === 'deptId'\">\n                      <dept-select\n                        v-model=\"form.deptId\"\n                        is-current\n                        :isAllData=\"!$editable.value\"\n                      />\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'manager'\">\n                      <user-select\n                        v-model=\"form.manager\"\n                        :placeholder=\"managePlaceholder\"\n                        :userdata=\"userdata\"\n                        multiple\n                        @setPhone=\"handleUserSelect\"\n                      />\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'domainId'\">\n                      <NetworkSelect v-model=\"form.domainId\"/>\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'tags'\">\n                      <Dynamic-Tag v-model=\"form.tags\"/>\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'vendor'\">\n                      <VendorSelect2\n                        v-model=\"form.vendors\"\n                        multiple\n                        placeholder=\"请选择开发合作企业\"\n                        :vendorsdata=\"vendorsdata\"\n                        :selectVendor=\"selectVendor\"\n                        :isDisabled=\"!$editable.value\"/>\n                    </template>\n\n                    <template v-else-if=\"field.fieldKey === 'waitingInsuranceFilingScan' || field.fieldKey === 'evaluationReport' || field.fieldKey === 'netTopo'\">\n                      <file-upload\n                        v-model=\"form[field.fieldKey]\"\n                        :limit=\"5\"\n                        :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                      />\n                    </template>\n\n                    <!-- 默认字段渲染 -->\n                    <template v-else>\n                      <!-- 下拉选择框 -->\n                      <el-select\n                        v-if=\"selectType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        :placeholder=\"field.placeholder || `请选择${field.fieldName}`\"\n                        :popper-append-to-body=\"false\"\n                        clearable\n                        filterable\n                        v-bind=\"field.props\"\n                      >\n                        <el-option\n                          v-for=\"item in getDictOptions(field.fieldKey)\"\n                          :key=\"item.value\"\n                          :label=\"item.label\"\n                          :value=\"item.value\"\n                        />\n                      </el-select>\n\n                      <!-- 日期选择器 -->\n                      <el-date-picker\n                        v-else-if=\"dateType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        :type=\"getDateType(field)\"\n                        :placeholder=\"field.placeholder || `请选择${field.fieldName}`\"\n                        v-bind=\"field.props\"\n                      />\n\n                      <!-- 单选按钮组 -->\n                      <el-radio-group\n                        v-else-if=\"radioGroupType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        v-bind=\"field.props\"\n                      >\n                        <el-radio\n                          v-for=\"item in dict.type.sys_yes_no\"\n                          :key=\"item.value\"\n                          :label=\"item.value\"\n                          @change=\"field.fieldKey === 'isadapt' ? showDapt : field.fieldKey === 'iscipher' ? showCipher : null \"\n                        >{{ item.label }}</el-radio>\n                      </el-radio-group>\n\n                      <!-- 多行文本输入 -->\n                      <el-input\n                        v-else-if=\"textareaType.includes(field.fieldKey)\"\n                        v-model=\"form[field.fieldKey]\"\n                        type=\"textarea\"\n                        :rows=\"3\"\n                        :placeholder=\"field.placeholder || `请输入${field.fieldName}`\"\n                        v-bind=\"field.props\"\n                      />\n\n                      <!-- 文件上传 -->\n                      <file-upload\n                        v-else-if=\"field.type === 'file'\"\n                        v-model=\"form[field.fieldKey]\"\n                        :limit=\"5\"\n                        :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                        v-bind=\"field.props\"\n                      />\n\n                      <!-- 默认文本输入 -->\n                      <el-input\n                        v-else\n                        v-model=\"form[field.fieldKey]\"\n                        :placeholder=\"field.placeholder || `请输入${field.fieldName}`\"\n                        v-bind=\"field.props\"\n                      />\n                    </template>\n                  </el-form-item>\n                </el-col>\n              </template>\n            </el-row>\n          </template>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <!--业务基本信息-->\n    <el-form ref=\"businessForm\" :model=\"businessForm\" :rules=\"businessRules\">\n      <el-row>\n        <el-col :span=\"24\">\n          <!-- 动态渲染业务信息字段 -->\n          <template v-for=\"group in businessAssetFields\">\n            <el-row :key=\"group.formName\" :gutter=\"20\" type=\"flex\" style=\"flex-wrap: wrap;margin: 20px 0;\">\n              <el-col :span=\"24\">\n                <div class=\"my-title\">\n                  <img v-if=\"group.formName === '用户规模' && group.isShow\" src=\"@/assets/images/application/yhgm.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '业务描述' && group.isShow\" src=\"@/assets/images/application/ywms.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '功能模块' && group.isShow\" src=\"@/assets/images/application/gnmk.png\" alt=\"\"/>\n                  {{ group.formName }}\n                </div>\n              </el-col>\n              <div class=\"my-form\" v-if=\"group.isShow\">\n                <!-- 用户规模特殊处理 -->\n                <template v-if=\"group.formName === '用户规模'\">\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"覆盖地域\" prop=\"coverArea\">\n                      <el-select v-model=\"businessForm.coverArea\" placeholder=\"请选择覆盖地域\">\n                        <el-option\n                          v-for=\"dict in dict.type.cover_area\"\n                          :key=\"dict.value\"\n                          :label=\"dict.label\"\n                          :value=\"dict.value\"\n                        ></el-option>\n                      </el-select>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"使用对象\" prop=\"serviceGroup\">\n                      <dict-select v-model=\"businessForm.serviceGroup\" dict-name=\"serve_group\" placeholder=\"请选择使用对象\"\n                                   multiple\n                                   clearable/>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"授权用户数\" prop=\"userNums\">\n                      <el-input\n                        type=\"number\"\n                        min=\"0\"\n                        v-model=\"businessForm.userNums\" @input=\"val=>inputToString(val,'userNums')\"\n                        placeholder=\"输入用户数\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"系统日均访客量\" prop=\"everydayVisitNums\">\n                      <el-input\n                        type=\"number\"\n                        min=\"0\"\n                        v-model=\"businessForm.everydayVisitNums\"\n                        @input=\"val=>inputToString(val,'everydayVisitNums')\"\n                        placeholder=\"输入系统日均访客量\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"系统月均活跃人数\" prop=\"everydayActiveNums\">\n                      <el-input\n                        type=\"number\"\n                        min=\"0\"\n                        v-model=\"businessForm.everydayActiveNums\"\n                        @input=\"val=>inputToString(val,'everydayActiveNums')\"\n                        placeholder=\"输入系统月均活跃人数\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                </template>\n\n                <!-- 业务描述特殊处理 -->\n                <template v-else-if=\"group.formName === '业务描述'\">\n                  <el-col :span=\"24\">\n                    <el-form-item :label=\"'总体系统业务说明'\" prop=\"sysBusinessState\">\n                      <el-input :rows=\"6\" :maxlength=\"800\" v-model=\"businessForm.sysBusinessState\" type=\"textarea\"\n                                placeholder=\"请输入系统业务说明..\"/>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"上传操作手册\" prop=\"operateHandbook\">\n                      <file-upload :disUpload=\"!$editable.value\"\n                                   v-model=\"businessForm.operateHandbook\"\n                                   :limit=\"5\"\n                                   :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                      />\n                    </el-form-item>\n                  </el-col>\n                </template>\n\n                <!-- 功能模块特殊处理 -->\n                <template v-else-if=\"group.formName === '功能模块'\">\n                  <el-col :span=\"24\" class=\"my-form\">\n                    <div v-for=\"(item, index) in functionStateList\" :key=\"index\">\n                      <el-form ref=\"moduleForm\" :model=\"item\" :rules=\"moduleRule\" :disabled=\"!$editable.value\">\n                        <el-row :gutter=\"20\" type=\"flex\" align=\"middle\" style=\"flex-wrap: wrap;\">\n                          <el-col :span=\"4\">\n                            <el-form-item prop=\"moduleName\">\n                              <el-input v-model=\"item.moduleName\" placeholder=\"功能模块名称\"/>\n                            </el-form-item>\n                          </el-col>\n                          <el-col :span=\"18\">\n                            <el-form-item prop=\"moduleDesc\">\n                              <el-input :rows=\"3\" v-model=\"item.moduleDesc\" type=\"textarea\"\n                                        placeholder=\"请将该功能模块实现的功能、用途做全面的说明，并保证内容应正确、完整、一致和可验证。\"/>\n                            </el-form-item>\n                          </el-col>\n                          <el-col :span=\"2\">\n                            <el-button\n                              size=\"mini\"\n                              type=\"text\"\n                              icon=\"el-icon-remove\"\n                              @click=\"handleDel(item.moduleId,index)\"\n                            >删除\n                            </el-button>\n                          </el-col>\n                        </el-row>\n                      </el-form>\n                    </div>\n                    <el-row type=\"flex\" justify=\"end\" style=\"flex-wrap: wrap;\">\n                      <el-col :span=\"2\">\n                        <el-button\n                          type=\"primary\"\n                          plain\n                          icon=\"el-icon-plus\"\n                          size=\"mini\"\n                          @click=\"handleAdd\"\n                        >新增\n                        </el-button>\n                      </el-col>\n                    </el-row>\n                  </el-col>\n                </template>\n              </div>\n            </el-row>\n          </template>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <!--系统软硬件环境-->\n    <el-form ref=\"hardWareForm\">\n      <el-row>\n        <el-col :span=\"24\">\n          <!-- 动态渲染软硬件环境字段 -->\n          <template v-for=\"group in runTimeAssetFields\">\n            <el-row :key=\"group.formName\" type=\"flex\" style=\"flex-wrap: wrap;margin: 20px 0;\">\n              <el-col :span=\"24\">\n                <div class=\"my-title\">\n                  <img v-if=\"group.formName === '所安装服务器环境'\" src=\"@/assets/images/application/fwq.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '所安装数据库环境'\" src=\"@/assets/images/application/sjk.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '关联网络设备'\" src=\"@/assets/images/application/wlsb.png\" alt=\"\"/>\n                  <img v-else-if=\"group.formName === '关联安全设备'\" src=\"@/assets/images/application/aqsb.png\" alt=\"\"/>\n                  {{ group.formName }}\n                </div>\n              </el-col>\n              <el-col :span=\"24\">\n                <!-- 根据分组名称渲染不同子组件 -->\n                <template v-if=\"group.formName === '所安装服务器环境'\">\n                  <serverEV\n                    ref=\"serverEV\"\n                    :function-list.sync=\"functionStateList\"\n                    :asset-id=\"assetId\"\n                    :data-list=\"currentAssociationServer\"\n                    @selected=\"serverSelect\"\n                    v-if=\"afterInit\"\n                    :fields=\"group.fieldsItems\"\n                  />\n                </template>\n\n                <template v-else-if=\"group.formName === '所安装数据库环境'\">\n                  <dateEV\n                    ref=\"dateEV\"\n                    :function-list.sync=\"functionStateList\"\n                    :asset-id=\"assetId\"\n                    :fields=\"group.fieldsItems\"\n                  />\n                </template>\n\n                <template v-else-if=\"group.formName === '关联网络设备'\">\n                  <networkEV\n                    ref=\"networkEV\"\n                    :asset-id=\"assetId\"\n                    :fields=\"group.fieldsItems\"\n                  />\n                </template>\n\n                <template v-else-if=\"group.formName === '关联安全设备'\">\n                  <safeEV\n                    ref=\"safeEV\"\n                    :asset-id=\"assetId\"\n                    :fields=\"group.fieldsItems\"/>\n                </template>\n              </el-col>\n            </el-row>\n          </template>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <edit-server\n      ref=\"adds\"\n      title=\"添加服务器\"\n      :edit-flag-visible.sync=\"showAddServer\"\n      @cancel=\"addServerCancel\"\n      @confirm=\"addServerSuccess\"/>\n\n  </div>\n</template>\n\n<script>\nimport {addApplicationInfo, updateApplicationInfo, getApplication} from \"@/api/safe/application\";\nimport ApplicationLink from '@/views/hhlCode/component/application/applicationLink';\nimport ApplicationSite from '@/views/hhlCode/component/application/applicationSite';\nimport UserSelect from '@/views/hhlCode/component/userSelect';\nimport DeptSelect from '@/views/components/select/deptSelect';\nimport NetworkSelect from '@/views/components/select/networkSelect';\nimport DynamicTag from '@/components/DynamicTag';\nimport VendorSelect2 from '@/views/components/select/vendorSelect2';\nimport DictSelect from '@/views/components/select/dictSelect';\nimport {getValFromObject} from \"@/utils\";\nimport {generateSecureUUID, waitForValue} from \"@/utils/ruoyi\";\nimport {listVendorByApplication} from \"@/api/safe/vendor\";\nimport serverEV from \"@/views/hhlCode/component/application/applicationHardware/serverEV.vue\";\nimport dateEV from \"@/views/hhlCode/component/application/applicationHardware/dateEV.vue\";\nimport networkEV from \"@/views/hhlCode/component/application/applicationHardware/networkEV.vue\";\nimport safeEV from \"@/views/hhlCode/component/application/applicationHardware/safeEV.vue\";\nimport overViewSelect from \"@/views/components/select/overViewSelect.vue\";\nimport {listAllOverview} from \"@/api/safe/overview\";\nimport EditServer from \"@/views/safe/server/editServer.vue\";\n\nexport default {\n  name: \"systemDetails\",\n  components: {\n    EditServer,\n    overViewSelect,\n    safeEV,\n    networkEV,\n    dateEV,\n    serverEV,\n    ApplicationLink,\n    ApplicationSite,\n    UserSelect,\n    DeptSelect,\n    NetworkSelect,\n    DictSelect,\n    DynamicTag,\n    VendorSelect2,\n  },\n  dicts: [\n    'serve_group',\n    'cover_area',\n    'sys_yes_no',\n    'app_net_scale',\n    'construct_type',\n    'system_type',\n    'protection_grade',\n    'asset_state',\n    'app_login_type',\n    'app_technical',\n    'app_deploy',\n    'app_storage',\n    'evaluation_results',\n    'evaluation_status',\n    'is_open_network',\n    'hw_is_true_shut_down'\n  ],\n  inject: {\n    $editable: {\n      default: {value: true},\n    }\n  },\n  props: {\n    assetId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n    changeId: Function,\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    assetList: {\n      type: Array,\n      default: () => []\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      collapseNames: ['1', '2', '3', '4', '5'],\n      vendorsdata: '1',\n      userdata: '1',\n      functionStateList: [{}, {}, {}],\n      // 基本信息表单参数\n      form: {},\n      // 业务信息表单参数\n      businessForm: {\n        delList: []\n      },\n      // 表单校验\n      rules: {\n        assetName: [\n          {required: true, message: \"应用名称不能为空\", trigger: \"blur\"},\n          {min: 0, max: 64, message: '应用名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        assetClass: [\n          {required: true, message: \"资产分类不能为空\", trigger: \"blur\"},\n        ],\n        domainId : [\n          {required: true, message: \"主部署网络不能为空\", trigger: \"blur\"},\n        ],\n        domainUrl: [\n          {min: 0, max: 128, message: '域名不能超过 128 个字符', trigger: 'blur'},\n          {\n            pattern: /^(?=^.{3,255}$)(http(s)?:\\/\\/)?(www\\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\\d+)*(\\/\\w+\\.\\w+)*$/,\n            message: \"请输入正确的域名\",\n            trigger: ['blur', 'change']\n          },\n        ],\n        manager: [\n          {required: true, message: \"负责人不能为空\", trigger: \"blur\"},\n        ],\n        userId: [\n          // { required: true, message: \"用户ID不能为空\", trigger: \"blur\" }\n        ],\n        deptId: [\n          {required: true, message: \"单位不能为空\", trigger: \"blur\"},\n        ],\n        phone: [\n          {min: 0, max: 11, message: '联系电话不能超过 11 位', trigger: 'blur'},\n          {pattern: /^1[1|2|3|4|5|6|7|8|9][0-9]\\d{8}$/, message: \"请输入正确的联系电话\", trigger: ['blur', 'change']},\n        ],\n        url: [\n          {required: true, message: \"登录地址不能为空\", trigger: \"blur\"},\n          {min: 0, max: 128, message: '登录地址不能超过 128 个字符', trigger: 'blur'},\n          {\n            // 正则表达式用于验证 URL 格式\n            // 支持 http/https 协议，允许 IP 地址或域名，支持端口号和路径\n            //pattern: /^(https?:\\/\\/)?(([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(:\\d+)?(\\/[\\w.-]*)*$/,\n            //pattern: /^(?:#\\/?[^\\s#]+|(https?|ftp):\\/\\/([\\w.-]+|\\[[\\da-fA-F:]+\\])(:\\d+)?(\\/[^?\\s#]*)?(\\?[^\\s#]*)?(#.*)?)$/,\n            pattern: /^(https?:\\/\\/)?((([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,})|((\\d{1,3}\\.){3}\\d{1,3}))(:\\d+)?(\\/[^\\s?#]*)?(\\?[^\\s#]*)?(#.*)?$/,\n            message: \"请输入正确的登录地址\",\n            trigger: ['blur', 'change']\n          }\n        ],\n        ipd: [\n          {required: true, message: \"Ip地址段不能为空\", trigger: \"blur\"},\n          {min: 0, max: 320, message: 'IP地址段填写已上限', trigger: 'blur'},\n          {\n            pattern: /^((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}(,((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3})*$/,\n            message: \"请输入正确的IP地址段，多个IP地址用逗号隔开\",\n            trigger: ['blur', 'change']\n          },\n        ],\n\n        netMemo: [\n          {min: 0, max: 255, message: '拓扑图说明不能超过 255 个字符', trigger: 'blur'},\n        ],\n      },\n      businessRules: {\n        sysBusinessState: [\n          {required: false, min: 0, max: 800, message: '拓扑图说明不能超过 800 个字符', trigger: 'blur'},\n        ],\n        userNums: [\n          {required: false, max: 12, message: '用户数量不能超过 12 个字符', trigger: 'blur'},\n          {required: false, pattern: /^[0-9]*$/, message: '请输入大于等于0的数字', trigger: 'blur'},\n        ],\n        everydayVisitNums: [\n          {min: 0, max: 12, message: '日均访问数量不能超过 12 个字符'},\n          {required: false, pattern: /^[0-9]*?$/, message: '请输入大于等于0的数字', trigger: 'blur'},\n        ],\n        everydayActiveNums: [\n          {min: 0, max: 12, message: '月均活跃人数不能超过 12 个字符'},\n          {required: false, pattern: /^[0-9]*?$/, message: '请输入大于等于0的数字', trigger: 'blur'},\n        ],\n      },\n      moduleRule: {\n        moduleName: [\n          {min: 0, max: 64, message: '功能模块名称不能超过 64 个字符', trigger: 'blur'},\n        ],\n        moduleDesc: [\n          {min: 0, max: 2000, message: '功能模块说明不能超过 2000 个字符', trigger: 'blur'},\n        ],\n      },\n      gv: getValFromObject,\n      deployLocation: localStorage.getItem(\"dl\"),\n      managerLabel: '责任人/电话',\n      managePlaceholder: '请选择责任人',\n      refs: {\n        'networkEV': \"所安装服务器环境\",\n        'safeEV': '所安装数据环境',\n        'serverEV': '关联网络设备',\n        'dateEV': \"关联安全设备\"\n      },\n      collapse: ['1', '2', '3', '4'],\n      showAddServer: false,\n      serverOptions: [],\n      currentAssociationServer: [],\n      afterInit: false,\n      selectType: ['systemType','construct','loginType','technical','deploy','state','protectGrade', 'evaluationResults', 'evaluationStatus', 'hwIsTrueShutDown'],\n      dateType: ['uodTime', 'waitingInsuranceFilingTime', 'evaluationYear'],\n      textareaType: ['netMemo', 'remark'],\n      radioGroupType: ['iskey', 'isbase', 'islog', 'isadapt', 'iscipher', 'isplan', 'islink', 'isOpenNetwork'],\n    }\n  },\n  mounted() {\n    this.getAllServerList();\n    this.$nextTick(() => {\n      if (this.deployLocation === 'fair') {\n        this.managerLabel = '责任民警/电话'\n        this.managePlaceholder = '请选择责任民警'\n      }\n      this.reset();\n      this.init()\n    });\n  },\n  activated() {\n    this.$nextTick(() => {\n      this.reset();\n      this.init()\n    });\n  },\n  watch: {\n    functionStateList: {\n      handler(newVal, oldVal) {\n        if (newVal && newVal.length > 0) {\n          newVal.forEach((item, index) => {\n            if(Object.keys(item).length > 0){\n              item.tempId = generateSecureUUID();\n            }\n          })\n        }\n      },\n    }\n  },\n  computed: {\n    // 业务信息动态字段\n    businessAssetFields() {\n      return (this.assetList.slice(7, 10) || []).map(group => ({\n        ...group,\n        fieldsItems: group.fieldsItems.filter(item => item.isShow)\n      }));\n    },\n    // 基本信息动态字段\n    basicInfoAssetFields() {\n      let assetFields = this.assetList.slice(0, 7);\n      return assetFields.map(group => {\n        return {\n          ...group,\n          fieldsItems: group.fieldsItems.filter(item => item.isShow)\n        };\n      })\n    },\n    // 软硬件环境动态字段\n    runTimeAssetFields() {\n      return (this.assetList.slice(10, 14) || []).map(group => ({\n        ...group,\n        fieldsItems: group.fieldsItems.filter(item => item.isShow)\n      }));\n    },\n    dynamicRules() {\n      const rules = {};\n      const ruleSets = {...this.rules, ...this.businessRules};\n      let visibleAssetFields = [...this.basicInfoAssetFields, ...this.businessAssetFields]\n      visibleAssetFields.forEach(group => {\n        group.fieldsItems.forEach(item => {\n          const fieldKey = item.fieldKey;\n          if (!rules[fieldKey]) {\n            rules[fieldKey] = [];\n          }\n\n          if (ruleSets[fieldKey]) {\n            const filteredRules = ruleSets[fieldKey].filter(rule => !rule.required);\n            rules[fieldKey].push(...filteredRules);\n          }\n          if (item.required) {\n            const hasRequiredRule = rules[fieldKey].some(rule => rule.required)\n            if (!hasRequiredRule) {\n              rules[fieldKey].push({\n                required: true,\n                message: `${item.fieldName}不能为空`,\n                trigger: ['blur', 'change']\n              });\n            }\n          }\n        })\n      })\n      return rules;\n    }\n  },\n  methods: {\n    // 字段校验规则\n    getFieldRules(field) {\n      return this.dynamicRules[field.fieldKey] || [];\n    },\n\n    // 获取字段所占列数\n    getFieldSpan(field) {\n      // 特殊字段占24列\n      const fullSpanFields = ['associationServer', 'netTopo', 'netMemo', 'evaluationReport', 'waitingInsuranceFilingScan', 'remark'];\n      if (fullSpanFields.includes(field.fieldKey)) return 24;\n      // 其他字段默认占8列\n      return 8;\n    },\n\n    shouldShowField(field) {\n      // 其他系统备注 - 只在 systemType 为 12 时显示\n      if (field.fieldKey === 'otherSystemNotes') {\n        return this.form.systemType == 12;\n      }\n\n      // 信创适配时间 - 只在 isadapt 为 'Y' 时显示\n      if (field.fieldKey === 'adaptDate') {\n        return this.form.isadapt === 'Y';\n      }\n\n      // 密码应用建设时间 - 只在 iscipher 为 'Y' 时显示\n      if (field.fieldKey === 'cipherDate') {\n        return this.form.iscipher === 'Y';\n      }\n\n      // 警综对接 - 只在特定部署位置显示\n      if (field.fieldKey === 'islink') {\n        return this.deployLocation === 'fair';\n      }\n\n      // 其他字段默认显示\n      return true;\n    },\n\n    getDictOptions(fieldKey) {\n      const dictMap = {\n        systemType: 'system_type',\n        construct: 'construct_type',\n        loginType: 'app_login_type',\n        technical: 'app_technical',\n        deploy: 'app_deploy',\n        state: 'asset_state',\n        protectGrade: 'protection_grade',\n        evaluationResults: 'evaluation_results',\n        evaluationStatus: 'evaluation_status',\n        hwIsTrueShutDown: 'hw_is_true_shut_down'\n      };\n\n      return this.dict.type[dictMap[fieldKey]] || [];\n    },\n\n    // 获取字段的日期类型\n    getDateType(field) {\n      switch (field.fieldKey) {\n        case 'uodTime':\n          return 'date';\n          case 'evaluationYear':\n            return 'year';\n        default:\n          return 'date';\n      }\n    },\n\n    //信创适配时间显示\n    showDapt() {\n      if (this.form.isadapt === 'N') {\n        this.form.adaptDate = null;\n      }\n    },\n    //密屏应用建设时间显示\n    showCipher() {\n      if (this.form.iscipher === 'N') {\n        this.form.cipherDate = null;\n      }\n    },\n\n    selectVendor(params) {\n      if (this.form.vendors == null || this.form.vendors == '') {\n        this.vendorsdata = null;\n      } else {\n        this.vendorsdata = '1';\n      }\n      return listVendorByApplication({\n        applicationId: this.assetId,\n        applicationCode: this.form.vendors,\n        ...params\n      });\n    },\n    getAllServerList(){\n      listAllOverview({\"assetClass\":4}).then(res =>{\n        this.serverOptions = res.data;\n      })\n    },\n    /** 初始化 */\n    async init() {\n      // let params = this.$route.query;\n      if (this.assetId) {\n        await getApplication(this.assetId).then(response => {\n          // 获取应用信息详情\n          this.form.assetId = this.assetId;\n          this.form = response.data.applicationVO;\n          if(response.data && response.data.applicationVO && response.data.applicationVO.systemType){\n            this.form.systemType = response.data.applicationVO.systemType.toString();\n          }\n          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {\n            if(!site){\n              return;\n            }\n            if(site instanceof Array){\n              site.forEach(item => item.getList());\n            }else {\n              site.getList()\n            }\n          })\n          // 获取业务信息详情\n          this.businessForm.assetId = this.assetId;\n          this.businessForm = response.data.tblBusinessApplication;\n          this.businessForm.userNums = this.businessForm.userNums !== null ? this.businessForm.userNums + '' : '';\n          this.businessForm.everydayVisitNums = this.businessForm.everydayVisitNums !== null ? this.businessForm.everydayVisitNums + '' : '';\n          this.businessForm.everydayActiveNums = this.businessForm.everydayActiveNums !== null ? this.businessForm.everydayActiveNums + '' : '';\n          this.functionStateList = response.data.tblBusinessApplication.tblMapperList || [{}, {}, {}];\n          if (this.functionStateList.length < 3) {\n            let i = 0;\n            while (i < 3 - this.functionStateList.length) {\n              this.functionStateList.push({});\n            }\n          }\n        }).finally(()=>{\n          this.afterInit = true;\n        })\n      }else {\n        this.afterInit = true;\n      }\n    },\n\n    // 校验数据\n    validateForm() {\n      let flag = true;\n      this.$refs['form'].validate(valid => {\n        if (!valid) {\n          flag = false;\n        }\n      });\n      this.$refs['businessForm'].validate(valid => {\n        if (!valid) {\n          flag = false;\n        }\n      });\n      return flag;\n    },\n\n    /** 保存按钮操作 */\n    handleSave() {\n      return new Promise((resolve, reject) => {\n        let pass1, pass2 = true;\n        this.$refs[\"form\"].validate(valid => pass1 = valid); // 系统基本信息校验\n        this.$refs[\"businessForm\"].validate(valid => pass2 = valid); // 业务信息校验\n\n        // 处理系统基本信息\n        let link = this.filterNull(this.form.links)\n        link.forEach(l => {\n          if (!(l.linkIp && l.linkIp.length > 0)) {\n            l.linkIp = null;\n          }\n          if (!(l.linkPort && l.linkPort.length > 0)) {\n            l.linkPort = null;\n          }\n        })\n        this.form.links = link;\n\n        // 处理业务信息\n        this.businessForm.tblMapperList = this.functionStateList.filter(fun => fun.moduleName);\n        for (let moduleFormKey in this.$refs.moduleForm) {\n          if (!pass2) return reject();\n          this.$refs.moduleForm[moduleFormKey].validate(valid => pass2 = valid)\n        }\n\n        console.log(this.$refs.networkEV, this.$refs.safeEV, this.$refs.serverEV, this.$refs.dateEV, 'sssssss')\n\n        // 处理软硬件信息\n        let form = {};\n        const hardwareComponents = [\n          'serverEV',\n          'dateEV',\n          'networkEV',\n          'safeEV'\n        ];\n\n        for (let ref of hardwareComponents) {\n          const component = this.$refs[ref];\n          if (!component) {\n            console.error(`${ref} 组件未找到`);\n            continue;\n          }\n\n          const compInstance = Array.isArray(component) ? component[0] : component;\n\n          try {\n            const data = compInstance.submit();\n            if (typeof data === 'string') {\n              this.$message.error(data);\n              return;\n            }\n            form[ref] = { list: data.list, delList: data.delList };\n          } catch (error) {\n            console.error(`调用 ${ref}.submit() 失败:`, error);\n            this.$message.error(`${ref} 提交失败: ${error.message}`);\n          }\n        }\n\n\n        // 整合参数\n        let params = {\n          serialVersionUID: 0,\n          applicationVO: this.form,\n          tblBusinessApplication: this.businessForm,\n          hardWareEV: form,\n        }\n\n        if (this.assetId != null) {\n          updateApplicationInfo(params).then(response => {\n            this.$modal.msgSuccess(\"修改成功\");\n            this.init()\n            return resolve();\n          }).catch((err) => {\n            return reject(err);\n          });\n        } else {\n          addApplicationInfo(params).then(response => {\n            this.form.assetId = response.data;\n            this.changeId(response.data);\n            this.$modal.msgSuccess(\"新增成功\");\n            this.init()\n            return resolve();\n          }).catch(err => {\n            return reject(err);\n          });\n        }\n      })\n    },\n\n    inputToString(val, name) {\n      if (val)\n        this.form[name] = \"\" + val;\n    },\n\n    handleAdd() {\n      this.functionStateList.push({});\n      this.functionStateList.forEach((item, index) => {\n        item.tempId = generateSecureUUID();\n      })\n    },\n    handleDel(moduleId, index) {\n      console.log('del',this.functionStateList)\n      this.functionStateList.splice(index, 1);\n      if (!this.businessForm.delList) {\n        this.businessForm.delList = [];\n      }\n      this.businessForm.delList.push(moduleId);\n    },\n\n    /** 表单重置 */\n    reset() {\n      this.form = {\n        assetId: undefined,\n        assetCode: undefined,\n        assetName: undefined,\n        softwareVersion: undefined,\n        degreeImportance: undefined,\n        manager: undefined,\n        domainUrl: undefined,\n        systemType: undefined,\n        phone: undefined,\n        assetType: undefined,\n        assetTypeDesc: undefined,\n        assetClass: undefined,\n        assetClassDesc: undefined,\n        construct: undefined,\n        netType: undefined,\n        appType: undefined,\n        serviceGroup: undefined,\n        frequency: undefined,\n        usageCount: undefined,\n        userScale: undefined,\n        userObject: undefined,\n        url: undefined,\n        ipd: undefined,\n        technical: undefined,\n        deploy: undefined,\n        storage: undefined,\n        netenv: undefined,\n        iskey: undefined,\n        datanum: undefined,\n        isbase: \"0\",\n        islink: undefined,\n        ishare: undefined,\n        islog: undefined,\n        isplan: undefined,\n        isadapt: undefined,\n        iscipher: undefined,\n        adaptDate: undefined,\n        cipherDate: undefined,\n        function: undefined,\n        remark: undefined,\n        userId: undefined,\n        deptId: undefined,\n        orgnId: undefined,\n        vendors: undefined,\n        upTime: undefined,\n        dwid: undefined,\n        contactor: undefined,\n        domainId: undefined,\n        netScale: undefined,\n        netTopo: undefined,\n        netMemo: undefined,\n        tags: \"\",\n        links: [],\n        eids: [],\n      };\n      this.businessForm = {\n        sysBusinessState: undefined,\n        userNums: undefined,\n        everydayVisitNums: undefined,\n        everydayActiveNums: undefined,\n      };\n      this.resetForm(\"form\");\n      this.resetForm(\"businessForm\");\n    },\n    /** 用户选择 */\n    handleUserSelect(val) {\n      if (this.form.manager == null || this.form.manager == '') {\n        this.userdata = null;\n      } else {\n        this.userdata = '1';\n      }\n      this.form.phone = val;\n    },\n    /** 过滤空值 */\n    filterNull(value) {\n      // return value.filter((item) => JSON.stringify(item) !== '{}');\n      return value.filter((item) => Object.keys(item).length !== 0);\n    },\n    addServerSuccess(row){\n      this.getAllServerList();\n      this.showAddServer = false;\n    },\n    addServerCancel(){\n      this.showAddServer = false;\n    },\n    addAssetHandle(){\n      this.showAddServer = true;\n    },\n    serverSelect(data){\n      if(data){\n        this.$set(this.form, 'associationServer', data.map(item => item.serverId))\n      }\n    },\n    serverChange(val){\n      console.log(\"server:\",val)\n      if(!val || val.length<1){\n        this.currentAssociationServer = [];\n      }else {\n        this.currentAssociationServer = val.map(item => this.serverOptions.find(server => server.assetId === item));\n      }\n      return val;\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n.box-container {\n  padding-right: 20px;\n  .my-form {\n    width: 100%;\n    margin: 0 10px;\n  }\n}\n::v-deep .el-select-dropdown {\n  position: absolute !important;\n  left: 0 !important;\n  top: 30px!important;\n}\n::v-deep .el-date-editor {\n  width: 100%;\n}\n\n::v-deep .associationServer{\n  .el-form-item__content{\n    display: flex;\n    align-items: center;\n  }\n  .el-form-item__label{\n    display: contents;\n  }\n}\n</style>\n"]}]}
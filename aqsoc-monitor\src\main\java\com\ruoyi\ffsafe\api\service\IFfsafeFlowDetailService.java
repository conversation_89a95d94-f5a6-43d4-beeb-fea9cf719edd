package com.ruoyi.ffsafe.api.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.ffsafe.api.domain.FfsafeFlowDetail;

import java.util.List;

/**
 * 非凡流量详情日志Service接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IFfsafeFlowDetailService {
    /**
     * 查询非凡流量详情日志
     *
     * @param id 非凡流量详情日志主键
     * @return 非凡流量详情日志
     */
    public FfsafeFlowDetail selectFfsafeFlowDetailById(Long id);

    /**
     * 批量查询非凡流量详情日志
     *
     * @param ids 非凡流量详情日志主键集合
     * @return 非凡流量详情日志集合
     */
    public List<FfsafeFlowDetail> selectFfsafeFlowDetailByIds(Long[] ids);

    /**
     * 查询非凡流量详情日志列表
     *
     * @param ffsafeFlowDetail 非凡流量详情日志
     * @return 非凡流量详情日志集合
     */
    public List<FfsafeFlowDetail> selectFfsafeFlowDetailList(FfsafeFlowDetail ffsafeFlowDetail);

    /**
     * 新增非凡流量详情日志
     *
     * @param ffsafeFlowDetail 非凡流量详情日志
     * @return 结果
     */
    public int insertFfsafeFlowDetail(FfsafeFlowDetail ffsafeFlowDetail);

    /**
     * 修改非凡流量详情日志
     *
     * @param ffsafeFlowDetail 非凡流量详情日志
     * @return 结果
     */
    public int updateFfsafeFlowDetail(FfsafeFlowDetail ffsafeFlowDetail);

    /**
     * 删除非凡流量详情日志信息
     *
     * @param id 非凡流量详情日志主键
     * @return 结果
     */
    public int deleteFfsafeFlowDetailById(Long id);

    /**
     * 批量删除非凡流量详情日志
     *
     * @param ids 需要删除的非凡流量详情日志主键集合
     * @return 结果
     */
    public int deleteFfsafeFlowDetailByIds(Long[] ids);

    List<FfsafeFlowDetail> selectList(FfsafeFlowDetail queryFlowDetail);

    List<JSONObject> groupThreatenName(FfsafeFlowDetail query);

    List<JSONObject> groupDip(FfsafeFlowDetail query);

    long count(FfsafeFlowDetail queryFlowDetail);
}

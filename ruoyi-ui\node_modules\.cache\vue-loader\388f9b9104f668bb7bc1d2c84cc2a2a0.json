{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\ffsafe-flow\\index.vue?vue&type=style&index=0&id=60611fcf&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\aqsoc\\ffsafe-flow\\index.vue", "mtime": 1754906332728}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmFzc2V0LXRhZ3sKICBtYXJnaW4tbGVmdDogNXB4OwogIG1heC13aWR0aDogMTAwJTsKICBvdmVyZmxvdzogaGlkZGVuOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKfQoub3ZlcmZsb3ctdGFnOm5vdCg6Zmlyc3QtY2hpbGQpewogIG1hcmdpbi10b3A6IDVweDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/aqsoc/ffsafe-flow", "sourcesContent": ["<template>\n  <div>\n    <div v-show=\"!formVisible\">\n      <div>\n        <JNPF-table :has-n-o=\"false\" v-loading=\"listLoading\" :data=\"list\" @sort-change='sortChange' :span-method=\"arraySpanMethod\" height=\"360px\">\n          <el-table-column prop=\"sip\" min-width=\"120\" label=\"攻击源IP\" align=\"left\">\n          </el-table-column>\n          <el-table-column prop=\"sport\" label=\"源端口\" width=\"90px;\" align=\"left\" />\n          <el-table-column prop=\"dip\" min-width=\"120\" label=\"目标IP\" align=\"left\">\n          </el-table-column>\n          <el-table-column prop=\"dport\" label=\"目标端口\" width=\"90px;\" align=\"left\" />\n<!--          <el-table-column label=\"关联业务系统\"  prop=\"businessApplicationList\" width=\"150\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <el-tooltip placement=\"bottom-end\" effect=\"light\" v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\" class=\"overflow-tag\" v-if=\"tagIndex <= 5\">\n                    <el-tag type=\"primary\"><span>{{item.assetName}}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\"><span>{{handleApplicationTagShow(scope.row.businessApplications)}}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>-->\n          <el-table-column prop=\"procotol\" label=\"协议\" width=\"90px;\" align=\"left\" />\n<!--          <el-table-column prop=\"threatenName\" min-width=\"120\" label=\"告警名称\" align=\"left\" />\n          <el-table-column prop=\"threatenType\" min-width=\"120\" label=\"攻击类型\" align=\"left\" />-->\n          <el-table-column prop=\"createTime\" min-width=\"120\" label=\"告警时间\" align=\"left\" />\n          <el-table-column prop=\"syncStatus\" min-width=\"120\" label=\"同步状态\" align=\"left\" />\n          <el-table-column label=\"操作\" :show-overflow-tooltip=\"false\" fixed=\"right\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"text\" @click=\"addOrUpdateHandle(scope.row, true)\">详情</el-button>\n            </template>\n          </el-table-column>\n        </JNPF-table>\n        <pagination :total=\"total\" :page.sync=\"listQuery.pageNum\" :limit.sync=\"listQuery.pageSize\" layout=\"total, prev, pager, next, jumper\" @pagination=\"initData\" />\n      </div>\n    </div>\n    <big-form :visible.sync=\"formVisible\" ref=\"BigForm\" @refresh=\"refresh\" />\n  </div>\n</template>\n\n<script>\n  import BigForm from './big-form.vue'\n  import {mapGetters} from \"vuex\";\n  import {getList,delData} from '@/api/aqsoc/threaten-web-shell/crud'\n  import { getFfsafeFlowDetailList } from '../../../api/threat/threat'\n\n  export default {\n    name:'FfsafeFlowDetail',\n    components: {BigForm},\n    props:{\n      view:{\n        type: Number,\n        require: false,\n        default:0\n      },\n      alarm:{\n        type: Object,\n        require: false\n      },\n      showIpTag: {\n        type: Boolean,\n        require: false,\n        default: false\n      }\n    },\n    data() {\n      return {\n        keyword: '',\n        expandObj: {},\n        query: {\n        },\n        list: [],\n        listLoading: true,\n        formVisible: false,\n        total: 0,\n        mergeList: [],\n        listQuery: {\n          // currentPage: 1,\n          pageNum: 1,\n          pageSize: 10\n        },\n      }\n    },\n    computed: {\n      ...mapGetters(['userInfo']),\n      menuId() {\n        return this.$route.meta.modelId || ''\n      }\n    },\n    created() {\n      this.initSearchDataAndListData()\n    },\n    methods: {\n      addOrUpdateHandle(row, isDetail, isAudit) {\n        this.formVisible = true\n        this.$nextTick(() => {\n          this.$refs.BigForm.init(row, isDetail, isAudit)\n        })\n      },\n      arraySpanMethod({column}) {\n        for (let i = 0; i < this.mergeList.length; i++) {\n          if (column.property == this.mergeList[i].prop) {\n            return [this.mergeList[i].rowspan, this.mergeList[i].colspan]\n          }\n        }\n      },\n      sortChange({column, prop, order}) {\n        this.initData()\n      },\n      async initSearchDataAndListData() {\n        this.initData()\n      },\n      initData() {\n        this.listLoading = true;\n        let _query = {\n          ...this.listQuery,\n          ...this.query,\n          keyword: this.keyword,\n          menuId: this.menuId\n        };\n        if(this.alarm){\n          _query.dip = this.alarm.destIp\n          _query.threatenType = this.alarm.threatenType\n          _query.threatenName = this.alarm.srcThreatenName\n          _query.sip = this.alarm.srcIp;\n          _query.dport = this.alarm.destPort;\n          _query.dataSource = this.alarm.dataSource;\n        }\n        getFfsafeFlowDetailList(_query).then(res => {\n          this.list = res.rows\n          this.total = res.total\n          this.listLoading = false\n        })\n      },\n      search() {\n        // this.listQuery.currentPage = 1\n        this.listQuery.pageNum = 1\n        this.listQuery.pageSize = 10\n        this.listQuery.sort = \"desc\"\n        this.listQuery.sidx = \"\"\n        this.initData()\n      },\n      refresh(isrRefresh) {\n        this.formVisible = false\n        if (isrRefresh) this.reset()\n      },\n      reset() {\n        for (let key in this.query) {\n          this.query[key] = undefined\n        }\n        this.search()\n      },\n      handleApplicationTagShow(applicationList){\n        if(!applicationList || applicationList.length < 1){\n          return '';\n        }\n        let result = applicationList[0].assetName;\n        if(applicationList.length > 1){\n          result += '...';\n        }\n        return result;\n      },\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n.asset-tag{\n  margin-left: 5px;\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n.overflow-tag:not(:first-child){\n  margin-top: 5px;\n}\n</style>\n"]}]}
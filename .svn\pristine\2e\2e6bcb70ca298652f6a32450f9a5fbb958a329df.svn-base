<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" :disabled="!$editable.value">
      <el-row>
        <el-col :span="22">
          <el-row :gutter="10" type="flex" style="flex-wrap: wrap;margin-bottom: 10px;">
            <el-col :span="24" class="mb8">
              <el-divider direction="vertical"></el-divider>
              <div class="my-title">基本信息</div>
            </el-col>
            <el-col :span="8">
              <el-form-item label="系统名称" prop="assetName">
                <el-input v-model="form.assetName" placeholder="请输入应用名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="登录地址" prop="url">
                <el-input v-model="form.url" placeholder="请输入登录地址"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="域名" prop="domainUrl">
                <el-input v-model="form.domainUrl" placeholder="请输入域名"/>
              </el-form-item>
            </el-col>


            <el-col :span="24">
              <el-form-item label="IP地址段" prop="ipd">
                <el-input :autosize="{minRows: 3, maxRows: 3}" v-model="form.ipd" type="textarea"
                          placeholder="请输入IP地址段，连续IP地址段格式为“***********-100”，单个IP需用分隔符号“,”。"/>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="所属单位" prop="deptId">
                <dept-select v-model="form.deptId" is-current :isAllData="!$editable.value"/>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item :label="managerLabel" prop="manager">
                <user-select v-model="form.manager" :placeholder="managePlaceholder" :userdata="userdata" multiple
                             @setPhone="handleUserSelect"/>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="系统类型" prop="systemType">
                <el-select v-model="form.systemType" placeholder="请选择系统类型" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.system_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="Number(dict.value)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="建设模式" prop="construct">
                <el-select v-model="form.construct" placeholder="请选择建设模式" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.construct_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="登录方式" prop="loginType">
                <el-select v-model="form.loginType" placeholder="请选择登录方式" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.app_login_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="技术架构" prop="technical">
                <el-select v-model="form.technical" placeholder="请选择技术架构" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.app_technical"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主部署方式" prop="deploy">
                <el-select v-model="form.deploy" placeholder="请选择主部署方式" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.app_deploy"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主部署网络" prop="domainId">
                <NetworkSelect v-model="form.domainId"/>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="上线状态" prop="state">
                <el-select v-model="form.state" placeholder="请选择上线状态" clearable>
                  <el-option
                    v-for="dict in dict.type.asset_state"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上线时间" prop="uodTime">
                <el-date-picker
                  v-model="form.uodTime"
                  type="date"
                  placeholder="选择上线时间"
                  format="yyyy 年 MM 月 dd 日"
                  value-format="yyyy-MM-dd HH:mm:ss"

                >
                </el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="关键基础设施" prop="iskey">
                <el-radio-group v-model="form.iskey">
                  <el-radio
                    v-for="dict in dict.type.sys_yes_no"
                    :key="dict.value"
                    :label="dict.value"

                  >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="开发合作企业" prop="vendor">
                <VendorSelect2 v-model="form.vendors" multiple placeholder="请选择开发合作企业"
                               :vendorsdata="vendorsdata" :selectVendor="selectVendor" :isDisabled="!$editable.value"/>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="产品名称">
                <el-input v-model="form.productName" placeholder="请输入产品名称"/>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="版本号">
                <el-input v-model="form.versionNumber" placeholder="请输入版本号"/>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="自定义标签" prop="tags">
                <Dynamic-Tag v-model="form.tags"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider class="hr"></el-divider>
          <el-row :gutter="10" type="flex" style="flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;">
            <el-col :span="24" class="mb8">
              <el-divider direction="vertical"></el-divider>
              <div class="my-title">备案信息</div>
            </el-col>
            <el-col :span="8">
              <el-form-item label="等保级别" prop="protectGrade">
                <el-select v-model="form.protectGrade" placeholder="请选择等保级别" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.protection_grade"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备案编号">
                <el-input v-model="form.waitingInsuranceFilingNumber" placeholder="请输入备案编号"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备案时间">
                <el-date-picker
                  v-model="form.waitingInsuranceFilingTime"
                  type="date"
                  placeholder="选择备案时间"
                  format="yyyy 年 MM 月 dd 日"
                  value-format="yyyy-MM-dd HH:mm:ss"

                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备案扫描件">
                <file-upload
                  :disUpload="!$editable.value"
                  v-model="form.waitingInsuranceFilingScan"
                  :limit="5"
                  :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider class="hr"></el-divider>
          <el-row :gutter="10" type="flex" style="flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;">
            <el-col :span="24" class="mb8">
              <el-divider direction="vertical"></el-divider>
              <div class="my-title">测评信息</div>
            </el-col>
            <el-col :span="8">
              <el-form-item label="测评年份">
                <el-date-picker
                  v-model="form.evaluationYear"
                  type="year"
                  placeholder="请选择测评年份">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="测评机构名称">
                <el-input v-model="form.evaluationAgency" placeholder="请输入测评机构名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="测评报告编号">
                <el-input v-model="form.evaluationNumber" placeholder="请输入测评报告编号"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="测评结论">
<!--                <el-input v-model="form.evaluationResults" placeholder="请输入测评结论"/>-->
                <el-select v-model="form.evaluationResults" placeholder="请选择等保级别" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.evaluation_results"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="测评得分">
                <el-input v-model="form.evaluationScore" placeholder="请输入测评得分"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="状态">
<!--                <el-input v-model="form.evaluationstatus" placeholder="请选择状态"/>-->
                <el-select v-model="form.evaluationstatus" placeholder="请选择状态" filterable clearable>
                  <el-option
                    v-for="dict in dict.type.evaluation_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="测评报告">
                <file-upload
                  :disUpload="!$editable.value"
                  v-model="form.evaluationReport"
                  :limit="5"
                  :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider class="hr"></el-divider>
          <el-row :gutter="10" type="flex" style="flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;">
            <el-col :span="24" class="mb8">
              <el-divider direction="vertical"></el-divider>
              <div class="my-title">外部连接信息</div>
            </el-col>
            <el-col :span="24">
              <ApplicationLink :disabled="!$editable.value" v-model="form.links"/>
            </el-col>
          </el-row>
          <el-divider class="hr"></el-divider>
          <el-row :gutter="10" type="flex" style="flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;">
            <el-col :span="24" class="mb8">
              <el-divider direction="vertical"></el-divider>
              <div class="my-title">拓扑结构信息</div>
            </el-col>
            <el-col :span="24">
              <el-form-item label="网络拓扑图" prop="netTopo">
                <file-upload
                  :disUpload="!$editable.value"
                  v-model="form.netTopo"
                  :limit="5"
                  :file-type="['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="拓扑图说明" prop="netMemo">
                <el-input v-model="form.netMemo" :autosize="{minRows: 6, maxRows: 6}" type="textarea"
                          placeholder="请输入拓扑图说明"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider class="hr"></el-divider>
          <el-row :gutter="10" type="flex" style="flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;">
            <el-col :span="24" class="mb8">
              <el-divider direction="vertical"></el-divider>
              <div class="my-title">运营维护情况</div>
            </el-col>
            <el-col :span="24">
              <ApplicationSite ref="site" :disabled="!$editable.value" :value.sync="form.eids" :asset-id="form.assetId" multiple/>
            </el-col>
          </el-row>
          <el-divider class="hr"></el-divider>
          <el-row :gutter="0" type="flex" style="flex-wrap: wrap;margin-top: 40px;margin-bottom: 20px;width: 50%;">
            <el-col :span="24" class="mb8">
              <el-divider direction="vertical"></el-divider>
              <div class="my-title">其他基本信息</div>
            </el-col>
            <el-col :span="12">
              <el-form-item label="基层数据采集">
                <el-radio-group v-model="form.isbase">
                  <el-radio
                    v-for="dict in dict.type.sys_yes_no"
                    :key="dict.value"
                    :label="dict.value"
                  >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="日志安审平台" prop="islog">
                <el-radio-group v-model="form.islog">
                  <el-radio
                    v-for="dict in dict.type.sys_yes_no"
                    :key="dict.value"
                    :label="dict.value"
                  >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否信创适配" prop="isadapt">
                <el-radio-group v-model="form.isadapt">
                  <el-radio
                    v-for="dict in dict.type.sys_yes_no"
                    :key="dict.value"
                    :label="dict.value"
                    @change="showDapt()">{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="信创适配时间" prop="adaptDate" v-if="form.isadapt=='Y'">
                <el-date-picker
                  clearable
                  v-model="form.adaptDate"
                  type="date"
                  format="yyyy 年 MM 月 dd 日"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择信创适配时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否密码应用" prop="iscipher">
                <el-radio-group v-model="form.iscipher">
                  <el-radio
                    v-for="dict in dict.type.sys_yes_no"
                    :key="dict.value"
                    :label="dict.value"
                    @change="showCipher()">{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码应用建设时间" prop="cipherDate" v-if="form.iscipher=='Y'">
                <el-date-picker
                  clearable
                  v-model="form.cipherDate"
                  type="date"
                  format="yyyy 年 MM 月 dd 日"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择建设时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="升级改造计划" prop="isplan">
                <el-radio-group v-model="form.isplan">
                  <el-radio
                    v-for="dict in dict.type.sys_yes_no"
                    :key="dict.value"
                    :label="dict.value"
                  >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="deployLocation === 'fair'">
              <el-form-item label="警综对接" prop="islink">
                <el-radio-group v-model="form.islink">
                  <el-radio
                    v-for="dict in dict.type.sys_yes_no"
                    :key="dict.value"
                    :label="dict.value"
                  >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import {getApplicationInfo, addApplicationInfo, updateApplicationInfo, getApplication} from "@/api/safe/application";
import ApplicationLink from '@/views/hhlCode/component/application/applicationLink';
import ApplicationSite from '@/views/hhlCode/component/application/applicationSite';
import UserSelect from '@/views/hhlCode/component/userSelect';
import DeptSelect from '@/views/components/select/deptSelect';
import NetworkSelect from '@/views/components/select/networkSelect';
import DynamicTag from '@/components/DynamicTag';
import VendorSelect2 from '@/views/components/select/vendorSelect2';
import DictSelect from '@/views/components/select/dictSelect';
import {getValFromObject} from "@/utils";
import {waitForValue} from "@/utils/ruoyi";
import {listVendorByApplication} from "@/api/safe/vendor";

export default {
  name: "applicationForm",
  components: {
    ApplicationLink,
    ApplicationSite,
    UserSelect,
    DeptSelect,
    NetworkSelect,
    DictSelect,
    DynamicTag,
    VendorSelect2,
  },
  dicts: [
    'sys_yes_no',
    'app_net_scale',
    'construct_type',
    'system_type',
    'protection_grade',
    'asset_state',
    'app_login_type',
    'app_technical',
    'app_deploy',
    'app_storage',
    'evaluation_results',
    'evaluation_status'
  ],
  inject: {
    $editable: {
      default: {value: true},
    }
  },
  props: {
    assetId: {
      type: [String, Number],
      required: false,
      default: null,
    },
    changeId: Function,
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      collapseNames: ['1', '2', '3', '4', '5'],
      vendorsdata: '1',
      userdata: '1',
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        assetName: [
          {required: true, message: "应用名称不能为空", trigger: "blur"},
          {min: 0, max: 64, message: '应用名称不能超过 64 个字符', trigger: 'blur'},
        ],
        assetClass: [
          {required: true, message: "资产分类不能为空", trigger: "blur"},
        ],
        domainUrl: [
          {min: 0, max: 128, message: '域名不能超过 128 个字符', trigger: 'blur'},
          {
            pattern: /^(?=^.{3,255}$)(http(s)?:\/\/)?(www\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\d+)*(\/\w+\.\w+)*$/,
            message: "请输入正确的域名",
            trigger: ['blur', 'change']
          },
        ],
        manager: [
          {required: true, message: "负责人不能为空", trigger: "blur"},
        ],
        userId: [
          // { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        deptId: [
          {required: true, message: "单位不能为空", trigger: "blur"},
        ],
        phone: [
          {min: 0, max: 11, message: '联系电话不能超过 11 位', trigger: 'blur'},
          {pattern: /^1[1|2|3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的联系电话", trigger: ['blur', 'change']},
        ],
        url: [
          {required: true, message: "登录地址不能为空", trigger: "blur"},
          {min: 0, max: 128, message: '登录地址不能超过 128 个字符', trigger: 'blur'},
        ],
        ipd: [
          {min: 0, max: 320, message: 'IP地址段填写已上限', trigger: 'blur'},
          {required: true, message: "Ip地址段不能为空", trigger: "blur"}
        ],
        netMemo: [
          {min: 0, max: 255, message: '拓扑图说明不能超过 255 个字符', trigger: 'blur'},
        ],
        tags: [
          {min: 0, max: 255, message: '拓扑图说明不能超过 255 个字符', trigger: 'blur'},
        ],
      },
      gv: getValFromObject,
      deployLocation: localStorage.getItem("dl"),
      managerLabel: '责任人/电话',
      managePlaceholder: '请选择责任人'
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.deployLocation === 'fair') {
        this.managerLabel = '责任民警/电话'
        this.managePlaceholder = '请选择责任民警'
      }
      this.reset();
      this.init()
    });
  },
  activated() {
    this.$nextTick(() => {
      this.reset();
      this.init()
    });
  },

  methods: {
    //信创适配时间显示
    showDapt() {
      if (this.form.isadapt == 'N') {
        this.form.adaptDate = null;
      }
    },
    //密屏应用建设时间显示
    showCipher() {
      if (this.form.iscipher == 'N') {
        this.form.cipherDate = null;
      }
    },

    selectVendor(params) {
      if (this.form.vendors == null || this.form.vendors == '') {
        this.vendorsdata = null;
      } else {
        this.vendorsdata = '1';
      }
      return listVendorByApplication({
        applicationId: this.assetId,
        applicationCode: this.form.vendors,
        ...params
      });
    },
    /** 初始化 */
    init() {
      // let params = this.$route.query;
      if (this.assetId) {
        this.form.assetId = this.assetId;
      }
      if (this.form.assetId != null) {
        getApplicationInfo(this.form.assetId).then(response => {
          console.log(response.data,'data')
          this.form = response.data;
          waitForValue(() => getValFromObject('site', this.$refs, null)).then(site => {
            if(!site){
              return;
            }
            if(site instanceof Array){
              site.forEach(item => item.getList());
            }else {
              site.getList()
            }
          })
        });
      }
    },

    // 校验数据
    validateForm() {
      let flag = null
      this.$refs['form'].validate(valid => {
        if (valid) {
          flag = true
        } else {
          flag = false
        }
      })
      return flag
    },

    /** 保存按钮操作 */
    handleSave() {
      console.log(this.form,'ffff')
      return new Promise((resolve, reject) => {
        this.$refs["form"].validate(valid => {
          let link = this.filterNull(this.form.links)
          link.forEach(l => {
            if (!(l.linkIp && l.linkIp.length > 0)) {
              l.linkIp = null;
            }
            if (!(l.linkPort && l.linkPort.length > 0)) {
              l.linkPort = null;
            }
          })
          if (valid) {
            this.form.links = link;
            if (this.form.assetId != null) {
              updateApplicationInfo(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.init()
                return resolve();
              }).catch((err) => {
                return reject(err);
              });
            } else {
              addApplicationInfo(this.form).then(response => {
                this.form.assetId = response.data;
                this.changeId(response.data);
                this.$modal.msgSuccess("新增成功");
                this.init()
                return resolve();
              }).catch(err => {
                return reject(err);
              });
            }
          } else {
            this.$modal.msgWarning("参数校验失败，请输入正确的参数！");
            return reject();
          }
        });
      })
    },
    /** 表单重置 */
    reset() {
      this.form = {
        assetId: undefined,
        assetCode: undefined,
        assetName: undefined,
        softwareVersion: undefined,
        degreeImportance: undefined,
        manager: undefined,
        domainUrl: undefined,
        systemType: undefined,
        phone: undefined,
        assetType: undefined,
        assetTypeDesc: undefined,
        assetClass: undefined,
        assetClassDesc: undefined,
        construct: undefined,
        netType: undefined,
        appType: undefined,
        serviceGroup: undefined,
        frequency: undefined,
        usageCount: undefined,
        userScale: undefined,
        userObject: undefined,
        url: undefined,
        ipd: undefined,
        technical: undefined,
        deploy: undefined,
        storage: undefined,
        netenv: undefined,
        iskey: undefined,
        datanum: undefined,
        isbase: "0",
        islink: undefined,
        ishare: undefined,
        islog: undefined,
        isplan: undefined,
        isadapt: undefined,
        iscipher: undefined,
        adaptDate: undefined,
        cipherDate: undefined,
        function: undefined,
        remark: undefined,
        userId: undefined,
        deptId: undefined,
        orgnId: undefined,
        vendors: undefined,
        upTime: undefined,
        dwid: undefined,
        contactor: undefined,
        domainId: undefined,
        netScale: undefined,
        netTopo: undefined,
        netMemo: undefined,
        waitingInsuranceFilingNumber: undefined,
        waitingInsuranceFilingTime: undefined,
        waitingInsuranceFilingScan: undefined,
        productName: undefined,
        versionNumber: undefined,
        tags: "",
        links: [],
        eids: [],
      };
      this.resetForm("form");
    },
    /** 用户选择 */
    handleUserSelect(val) {
      if (this.form.manager == null || this.form.manager == '') {
        this.userdata = null;
      } else {
        this.userdata = '1';
      }
      this.form.phone = val;
    },
    /** 过滤空值 */
    filterNull(value) {
      // return value.filter((item) => JSON.stringify(item) !== '{}');
      return value.filter((item) => Object.keys(item).length !== 0);
    },
  },
}
</script>

<style lang="scss" scoped>
.el-divider {
  background: #0E94EA;
}

.el-divider--vertical {
  display: inline-block;
  width: 5px;
  height: 2em;
  margin: 0 8px 0 0;
  //vertical-align: middle;
  //position: relative;
}

.hr.el-divider {
  margin: 0;
  background: #DCDFE6;
  border: 1px solid #DCDFE6;
}

.el-date-editor.el-input {
  width: 100%;
}

.my-title {
  display: inline-block;
  vertical-align: center;
}
</style>

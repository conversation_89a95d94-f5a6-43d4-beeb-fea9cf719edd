{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\basicInfo.vue?vue&type=style&index=0&id=361e31c8&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\safe\\server\\basicInfo.vue", "mtime": 1754969114240}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgpAaW1wb3J0ICJAL2Fzc2V0cy9zdHlsZXMvY3VzdG9tRm9ybSI7CiAgLmNvbGxhcHNlLXRpdGxlIHsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgbWFyZ2luLWxlZnQ6IDIwcHg7CiAgfQogIC5jb2xsYXBzZS1yb3cgewogICAgbGluZS1oZWlnaHQ6IDMycHg7CiAgfQogIC5jb2xsYXBzZS1jb250ZW50LWRpdiB7CiAgICBtYXJnaW46IDAgMjBweDsKICB9CiAgLmNvbGxhcHNlLWxhYmVsIHsKICAgIGNvbG9yOiAjOTc5Nzk3OwogIH0KICAuY29sbGFwc2UtdmFsdWUgewogICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgfQogIC5lbXB0eSB7CiAgICBjb2xvcjogIzk3OTc5NzsKICB9Cg=="}, {"version": 3, "sources": ["basicInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "basicInfo.vue", "sourceRoot": "src/views/safe/server", "sourcesContent": ["<template>\n  <div class=\"customForm-container\" style=\"height: 100%; overflow: hidden\">\n    <template v-for=\"group in visibleAssetFields\">\n      <div :key=\"group.formName\">\n        <div class=\"my-title\">\n          <!-- 动态图标渲染 -->\n          <img v-if=\"group.formName === '基本信息'\" src=\"@/assets/images/application/jbxx.png\" alt=\"\"/>\n          <i v-else-if=\"group.formName === '硬件/软件概况信息'\" class=\"el-icon-cpu\" style=\"font-size: 24px; margin: 0 5px; color: #4382fd\" />\n          <i v-else-if=\"group.formName === '位置信息'\" class=\"el-icon-location-information\" style=\"font-size: 24px; margin: 0 5px; color: #4382fd\" />\n          <img v-else-if=\"group.formName === '网络信息'\" src=\"@/assets/images/application/netinfo.png\" alt=\"\"/>\n          {{ group.formName }}\n        </div>\n\n        <!-- 网络信息特殊处理 -->\n        <template v-if=\"group.formName === '网络信息'\">\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item label=\"外网IP\">\n              <el-col :class=\"[ !form.exposedIp ? 'empty' : 'collapse-value' ]\">\n                {{ form.exposedIp || '（空）' }}\n              </el-col>\n            </el-descriptions-item>\n          </el-descriptions>\n          <el-table :data=\"macAipList\" :header-cell-style=\"headerCellStyle\" :cell-style=\"cellStyle\">\n            <el-table-column label=\"是否主ip\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.mainIp === '1'\">是</span>\n                <span v-if=\"scope.row.mainIp === '0'\">否</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"domainFullName\" label=\"所属网络\" />\n            <el-table-column prop=\"ipv4\" label=\"ip\" />\n            <el-table-column prop=\"mac\" label=\"mac\" />\n          </el-table>\n        </template>\n\n        <!-- 其他分组正常渲染 -->\n        <template v-else>\n          <el-descriptions\n            class=\"custom-column\"\n            direction=\"vertical\"\n            size=\"medium\"\n            :colon=\"false\"\n            label-class-name=\"custom-label-style\"\n            content-class-name=\"custom-content-style\"\n            :column=\"3\">\n            <el-descriptions-item\n              v-for=\"field in group.fieldsItems\"\n              :key=\"field.fieldKey\"\n              :label=\"field.fieldName\">\n              <!-- 所属部门特殊处理 -->\n              <template v-if=\"field.fieldKey === 'deptId'\">\n                <span :class=\"[ !form.deptName ? 'empty' : 'collapse-value' ]\">\n                  {{ form.deptName || '（空）' }}\n                </span>\n              </template>\n\n              <!-- 其他字段正常渲染 -->\n              <template v-else>\n                <span :class=\"[ !getFieldValue(field) ? 'empty' : 'collapse-value' ]\">\n                  {{ getFieldDisplayValue(field) || '（空）' }}\n                </span>\n              </template>\n            </el-descriptions-item>\n          </el-descriptions>\n        </template>\n      </div>\n    </template>\n  </div>\n</template>\n\n<script>\nimport assetRegister from \"@/mixins/assetRegister\";\nexport default {\n  name: 'BasicInfo',\n  mixins: [assetRegister],\n  dicts: ['impt_grade', 'sys_yes_no', 'proc_type','is_sparing'],\n  props: {\n    form: {\n      type: Object,\n      default: null\n    },\n    edrForm: {\n      type: Object,\n      default: null\n    },\n    myTags: {\n      type: Array,\n      default: null\n    },\n    macAipList: {\n      type: Array,\n      default: null\n    }\n  },\n  data() {\n    return {\n      activeNames: ['1', '2', '3', '4'],\n      collapseLabelSpan: 4,\n      collapseContentSpan: 8,\n      headerCellStyle: { 'font-weight': 'normal', color: '#979797' },\n      cellStyle: { 'font-weight': 'bold' },\n      assetAllocationType: '2',\n    }\n  },\n  computed: {\n    visibleAssetFields() {\n      return (this.assetList || []).map(group => ({\n        ...group,\n        fieldsItems: group.fieldsItems.filter(item => item.isShow)\n      }));\n    }\n  },\n  mounted() {},\n  methods: {\n    getFieldValue(field) {\n      return this.form[field.fieldKey];\n    },\n    getFieldDisplayValue(field) {\n      const value = this.getFieldValue(field);\n      // 特殊字段处理\n      if (field.fieldKey === 'degreeImportance') {\n        return this.dict.type.impt_grade.find(d => d.value === value)?.label || value;\n      }\n      if (field.fieldKey === 'isVirtual') {\n        return value === 'Y' ? '是' : value === 'N' ? '否' : value;\n      }\n      if (field.fieldKey === 'isSparing') {\n        return this.dict.type.is_sparing.find(d => d.value === value)?.label || value;\n      }\n      if (field.fieldKey === 'maintainUnit') {\n        return this.form.maintainUnitName;\n      }\n      if (field.fieldKey === 'facilityManufacturer') {\n        return this.form.facilityManufacturerName || value;\n      }\n      if (field.fieldKey === 'vendor') {\n        return this.form.vendorName || value;\n      }\n      if (field.fieldKey === 'assetType') {\n        return this.form.assetTypeDesc || value;\n      }\n      return value;\n    }\n  },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"@/assets/styles/customForm\";\n  .collapse-title {\n    font-weight: bold;\n    margin-left: 20px;\n  }\n  .collapse-row {\n    line-height: 32px;\n  }\n  .collapse-content-div {\n    margin: 0 20px;\n  }\n  .collapse-label {\n    color: #979797;\n  }\n  .collapse-value {\n    font-weight: bold;\n  }\n  .empty {\n    color: #979797;\n  }\n</style>\n"]}]}
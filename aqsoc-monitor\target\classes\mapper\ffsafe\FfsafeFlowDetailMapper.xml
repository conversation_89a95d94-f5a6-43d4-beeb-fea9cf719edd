<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeFlowDetailMapper">

    <resultMap type="FfsafeFlowDetail" id="FfsafeFlowDetailResult">
        <result property="id"    column="id"    />
        <result property="sip"    column="sip"    />
        <result property="sport"    column="sport"    />
        <result property="dip"    column="dip"    />
        <result property="dport"    column="dport"    />
        <result property="procotol"    column="procotol"    />
        <result property="threatenName"    column="threaten_name"    />
        <result property="threatenType"    column="threaten_type"    />
        <result property="featureEn"    column="feature_en"    />
        <result property="feature"    column="feature"    />
        <result property="responseCode"    column="response_code"    />
        <result property="responseEn"    column="response_en"    />
        <result property="response"    column="response"    />
        <result property="probeId"    column="probe_id"    />
        <result property="probeIp"    column="probe_ip"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="alarmLevel"    column="alarm_level"    />
        <result property="payloadTitle"    column="payload_title"    />
        <result property="payloadEn"    column="payload_en"    />
        <result property="payload"    column="payload"    />
        <result property="metaData"    column="meta_data"    />
        <result property="createTime"    column="create_time"    />
        <result property="timeline"    column="timeline"    />
    </resultMap>

    <sql id="selectFfsafeFlowDetailVo">
        select id, sip, sport, dip, dport, procotol, threaten_name, threaten_type, feature_en, feature, response_code, response_en, response, probe_id, probe_ip, rule_id, alarm_level, payload_title, payload_en, payload, meta_data, create_time, timeline from ffsafe_flow_detail
    </sql>

    <select id="selectFfsafeFlowDetailList" parameterType="FfsafeFlowDetail" resultMap="FfsafeFlowDetailResult">
        select t1.id, t1.sip, t1.sport, t1.dip, t1.dport, t1.procotol, t1.threaten_name, t1.threaten_type, t1.feature_en, t1.feature, t1.response_code,
        t1.response_en, t1.response, t1.probe_id, t1.probe_ip, t1.rule_id, t1.alarm_level, t1.payload_title, t1.payload_en, t1.payload, t1.meta_data,
        t1.create_time, t1.timeline
        from ffsafe_flow_detail t1 USE INDEX(idx_count_optimize)
        <where>
            <if test="sport != null "> and t1.sport = #{sport}</if>
            <if test="dip != null  and dip != ''"> and t1.dip = #{dip}</if>
            <if test="dport != null "> and t1.dport = #{dport}</if>
            <if test="procotol != null  and procotol != ''"> and t1.procotol = #{procotol}</if>
            <if test="threatenName != null  and threatenName != ''"> and t1.threaten_name = #{threatenName}</if>
            <if test="threatenType != null  and threatenType != ''"> and t1.threaten_type = #{threatenType}</if>
            <if test="featureEn != null  and featureEn != ''"> and t1.feature_en = #{featureEn}</if>
            <if test="feature != null  and feature != ''"> and t1.feature = #{feature}</if>
            <if test="responseCode != null "> and t1.response_code = #{responseCode}</if>
            <if test="responseEn != null  and responseEn != ''"> and t1.response_en = #{responseEn}</if>
            <if test="response != null  and response != ''"> and t1.response = #{response}</if>
            <if test="probeId != null  and probeId != ''"> and t1.probe_id = #{probeId}</if>
            <if test="probeIp != null  and probeIp != ''"> and t1.probe_ip = #{probeIp}</if>
            <if test="ruleId != null "> and t1.rule_id = #{ruleId}</if>
            <if test="alarmLevel != null "> and t1.alarm_level = #{alarmLevel}</if>
            <if test="payloadTitle != null  and payloadTitle != ''"> and t1.payload_title = #{payloadTitle}</if>
            <if test="payloadEn != null  and payloadEn != ''"> and t1.payload_en = #{payloadEn}</if>
            <if test="payload != null  and payload != ''"> and t1.payload = #{payload}</if>
            <if test="metaData != null  and metaData != ''"> and t1.meta_data = #{metaData}</if>
            <if test="timeline != null "> and t1.timeline = #{timeline}</if>
            <if test="startTime != null and endTime != null "> and t1.create_time between #{startTime} and #{endTime}</if>
            <if test="sip != null  and sip != ''"> and t1.sip = #{sip}</if>
            and t1.create_time > DATE_ADD(now(),INTERVAL -7 day)
        </where>
        order by t1.create_time desc
    </select>

    <select id="selectFfsafeFlowDetailList_COUNT" parameterType="FfsafeFlowDetail" resultType="Long">
        select COUNT(1)
        from ffsafe_flow_detail t1 USE INDEX(idx_count_optimize)
        <where>
            <if test="sport != null "> and t1.sport = #{sport}</if>
            <if test="dip != null  and dip != ''"> and t1.dip = #{dip}</if>
            <if test="dport != null "> and t1.dport = #{dport}</if>
            <if test="procotol != null  and procotol != ''"> and t1.procotol = #{procotol}</if>
            <if test="threatenName != null  and threatenName != ''"> and t1.threaten_name = #{threatenName}</if>
            <if test="threatenType != null  and threatenType != ''"> and t1.threaten_type = #{threatenType}</if>
            <if test="featureEn != null  and featureEn != ''"> and t1.feature_en = #{featureEn}</if>
            <if test="feature != null  and feature != ''"> and t1.feature = #{feature}</if>
            <if test="responseCode != null "> and t1.response_code = #{responseCode}</if>
            <if test="responseEn != null  and responseEn != ''"> and t1.response_en = #{responseEn}</if>
            <if test="response != null  and response != ''"> and t1.response = #{response}</if>
            <if test="probeId != null  and probeId != ''"> and t1.probe_id = #{probeId}</if>
            <if test="probeIp != null  and probeIp != ''"> and t1.probe_ip = #{probeIp}</if>
            <if test="ruleId != null "> and t1.rule_id = #{ruleId}</if>
            <if test="alarmLevel != null "> and t1.alarm_level = #{alarmLevel}</if>
            <if test="payloadTitle != null  and payloadTitle != ''"> and t1.payload_title = #{payloadTitle}</if>
            <if test="payloadEn != null  and payloadEn != ''"> and t1.payload_en = #{payloadEn}</if>
            <if test="payload != null  and payload != ''"> and t1.payload = #{payload}</if>
            <if test="metaData != null  and metaData != ''"> and t1.meta_data = #{metaData}</if>
            <if test="timeline != null "> and t1.timeline = #{timeline}</if>
            <if test="startTime != null and endTime != null "> and t1.create_time between #{startTime} and #{endTime}</if>
            <if test="sip != null  and sip != ''"> and t1.sip = #{sip}</if>
            and t1.create_time > DATE_ADD(now(),INTERVAL -7 day)
        </where>
    </select>

    <select id="selectFfsafeFlowDetailById" parameterType="Long" resultMap="FfsafeFlowDetailResult">
        <include refid="selectFfsafeFlowDetailVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeFlowDetailByIds" parameterType="Long" resultMap="FfsafeFlowDetailResult">
        <include refid="selectFfsafeFlowDetailVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectList" resultType="com.ruoyi.ffsafe.api.domain.FfsafeFlowDetail" resultMap="FfsafeFlowDetailResult">
        SELECT id,sip,sport,dip,dport,procotol,threaten_name,threaten_type,alarm_level,create_time FROM ffsafe_flow_detail
        <where>
            <if test="sip != null and sip != ''">and sip = #{sip}</if>
            <if test="params != null and params.ips != null and params.ips.size()>0">
                and sip in
                <foreach item="ip" collection="params.ips" separator="," open="(" close=")" index="">
                    #{ip}
                </foreach>
            </if>
            <if test="startTime != null"> and create_time >= #{startTime}</if>
            <if test="endTime != null"> and create_time &lt;= #{endTime}</if>
        </where>
        <if test="params != null and params.groupBy != null and params.groupBy != ''">
            GROUP BY ${params.groupBy}
        </if>
    </select>
    <select id="groupThreatenName" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
        <where>
            <if test="sip != null and sip != ''">and sip = #{sip}</if>
            <if test="params != null and params.ips != null and params.ips.size()>0">
                and sip in
                <foreach item="ip" collection="params.ips" separator="," open="(" close=")" index="">
                    #{ip}
                </foreach>
            </if>
            <if test="startTime != null and endTime != null"> and create_time between #{startTime} and #{endTime}</if>
        </where>
        GROUP BY sip, threaten_name
    </select>
    <select id="groupDip" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        dip
        FROM ffsafe_flow_detail
        <where>
            <if test="sip != null and sip != ''">and sip = #{sip}</if>
            <if test="params != null and params.ips != null and params.ips.size()>0">
                and sip in
                <foreach item="ip" collection="params.ips" separator="," open="(" close=")" index="">
                    #{ip}
                </foreach>
            </if>
            <if test="startTime != null"> and create_time >= #{startTime}</if>
            <if test="endTime != null"> and create_time &lt;= #{endTime}</if>
        </where>
        GROUP BY dip
    </select>
    <select id="count" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM ffsafe_flow_detail
        <where>
            <if test="sip != null and sip != ''">and sip = #{sip}</if>
            <if test="params != null and params.ips != null and params.ips.size()>0">
                and sip in
                <foreach item="ip" collection="params.ips" separator="," open="(" close=")" index="">
                    #{ip}
                </foreach>
            </if>
            <if test="startTime != null"> and create_time >= #{startTime}</if>
            <if test="endTime != null"> and create_time &lt;= #{endTime}</if>
        </where>
    </select>

    <insert id="insertFfsafeFlowDetail" parameterType="FfsafeFlowDetail" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_flow_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sip != null">sip,</if>
            <if test="sport != null">sport,</if>
            <if test="dip != null">dip,</if>
            <if test="dport != null">dport,</if>
            <if test="procotol != null">procotol,</if>
            <if test="threatenName != null">threaten_name,</if>
            <if test="threatenType != null">threaten_type,</if>
            <if test="featureEn != null">feature_en,</if>
            <if test="feature != null">feature,</if>
            <if test="responseCode != null">response_code,</if>
            <if test="responseEn != null">response_en,</if>
            <if test="response != null">response,</if>
            <if test="probeId != null">probe_id,</if>
            <if test="probeIp != null">probe_ip,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="alarmLevel != null">alarm_level,</if>
            <if test="payloadTitle != null">payload_title,</if>
            <if test="payloadEn != null">payload_en,</if>
            <if test="payload != null">payload,</if>
            <if test="metaData != null">meta_data,</if>
            <if test="createTime != null">create_time,</if>
            <if test="timeline != null">timeline,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sip != null">#{sip},</if>
            <if test="sport != null">#{sport},</if>
            <if test="dip != null">#{dip},</if>
            <if test="dport != null">#{dport},</if>
            <if test="procotol != null">#{procotol},</if>
            <if test="threatenName != null">#{threatenName},</if>
            <if test="threatenType != null">#{threatenType},</if>
            <if test="featureEn != null">#{featureEn},</if>
            <if test="feature != null">#{feature},</if>
            <if test="responseCode != null">#{responseCode},</if>
            <if test="responseEn != null">#{responseEn},</if>
            <if test="response != null">#{response},</if>
            <if test="probeId != null">#{probeId},</if>
            <if test="probeIp != null">#{probeIp},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="alarmLevel != null">#{alarmLevel},</if>
            <if test="payloadTitle != null">#{payloadTitle},</if>
            <if test="payloadEn != null">#{payloadEn},</if>
            <if test="payload != null">#{payload},</if>
            <if test="metaData != null">#{metaData},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="timeline != null">#{timeline},</if>
        </trim>
    </insert>

    <update id="updateFfsafeFlowDetail" parameterType="FfsafeFlowDetail">
        update ffsafe_flow_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="sip != null">sip = #{sip},</if>
            <if test="sport != null">sport = #{sport},</if>
            <if test="dip != null">dip = #{dip},</if>
            <if test="dport != null">dport = #{dport},</if>
            <if test="procotol != null">procotol = #{procotol},</if>
            <if test="threatenName != null">threaten_name = #{threatenName},</if>
            <if test="threatenType != null">threaten_type = #{threatenType},</if>
            <if test="featureEn != null">feature_en = #{featureEn},</if>
            <if test="feature != null">feature = #{feature},</if>
            <if test="responseCode != null">response_code = #{responseCode},</if>
            <if test="responseEn != null">response_en = #{responseEn},</if>
            <if test="response != null">response = #{response},</if>
            <if test="probeId != null">probe_id = #{probeId},</if>
            <if test="probeIp != null">probe_ip = #{probeIp},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="alarmLevel != null">alarm_level = #{alarmLevel},</if>
            <if test="payloadTitle != null">payload_title = #{payloadTitle},</if>
            <if test="payloadEn != null">payload_en = #{payloadEn},</if>
            <if test="payload != null">payload = #{payload},</if>
            <if test="metaData != null">meta_data = #{metaData},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="timeline != null">timeline = #{timeline},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeFlowDetailById" parameterType="Long">
        delete from ffsafe_flow_detail where id = #{id}
    </delete>

    <delete id="deleteFfsafeFlowDetailByIds" parameterType="String">
        delete from ffsafe_flow_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

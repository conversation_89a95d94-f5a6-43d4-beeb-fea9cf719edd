# TblServer导入功能实现项目计划

## 项目概述

**目标**: 在TblServerController中新增importJtData方法，实现基于TblServerImportDTO的服务器数据导入功能

**背景**: 基于之前创建的TblServerImportDTO类和优化的importJtTemplate模板接口，现在需要实现实际的数据导入功能

**数据来源**: 使用importJtTemplate接口生成的Excel模板格式，包含14个核心字段

## 核心技术挑战

### 6种特殊字段处理逻辑

1. **设备厂商/供应商字段**: 自动查询或创建供应商记录
2. **业务系统字段**: 建立tbl_application_server关联，未匹配跳过
3. **主IP字段**: 使用IpAddressMatcher匹配网络区域
4. **MAC字段**: 保存到tbl_network_ip_mac表，建立IP-MAC关联
5. **操作系统/数据库字段**: 查询或创建产品记录，构建TblDeploy对象
6. **所属部门字段**: 必须匹配，不存在则报错阻止导入

### 性能优化策略

- **批量查询**: 一次性查询所有字典数据，避免N+1问题
- **事务管理**: 使用@Transactional确保数据一致性
- **错误收集**: 批量收集错误，统一返回用户友好信息

## 任务分解

### 第一阶段：基础框架搭建

**任务1**: 在TblServerController中新增importJtData方法
- ID: 567706e8-6807-4e3f-8b95-a04ae6e1bb97
- 关键点: Excel解析、基本验证、异常处理

**任务2**: 在TblServerService中新增接口方法声明
- ID: de88e2d7-6363-4beb-895e-05d756897fb0
- 关键点: 接口设计、JavaDoc注释

### 第二阶段：核心功能实现

**任务3**: 实现批量查询字典数据的方法
- ID: 4dea9f0b-d4ab-4bf1-86b0-cec40a3cfd8b
- 关键点: 性能优化、Map构建、空值处理

**任务4**: 实现IP匹配网络区域的方法
- ID: 41d84798-f245-4087-b7eb-407511403e1f
- 关键点: IpAddressMatcher、CIDR匹配

**任务5**: 实现供应商和产品的获取或创建方法
- ID: 69ad8eb8-a4de-4e03-aae0-fd70268c3b47
- 关键点: 自动创建、并发安全、Map更新

### 第三阶段：数据转换与保存

**任务6**: 实现DTO到实体的转换和特殊字段处理
- ID: 86f33c21-0391-4528-b396-64e93d6bf47a
- 关键点: 6种特殊逻辑、错误处理、数据验证

**任务7**: 实现关联表数据保存逻辑
- ID: c2333f60-b6b9-4dd4-a440-a5ae18e793e0
- 关键点: 业务系统关联、IP-MAC保存、批量优化

### 第四阶段：整合与完善

**任务8**: 实现主方法importServerFromTemplate
- ID: f30e9c1e-04f8-4bcb-ae41-d8e3ca0a753a
- 关键点: 完整流程、错误收集、事务管理

## 技术实现要点

### 方法签名设计
```java
@Log(title = "服务器/存储设备", businessType = BusinessType.IMPORT)
@PostMapping("/importJtData")
@Transactional(rollbackFor = Exception.class)
public AjaxResult importJtData(MultipartFile file) throws Exception
```

### 数据库表关系
- **主表**: tbl_server
- **关联表**: tbl_network_ip_mac, tbl_application_server
- **字典表**: tbl_vendor, tbl_business_application, sys_dept, tbl_product

### 错误处理策略
- Excel格式错误: 友好提示文件格式问题
- 必填字段验证: 明确指出行号和字段
- 部门不存在: 明确提示需要先创建部门
- 批量错误汇总: 收集所有错误统一返回

## 验证标准

- 能正确解析TblServerImportDTO格式的Excel文件
- 6种特殊字段处理逻辑正确实现
- 数据正确保存到tbl_server及相关关联表
- 异常情况有适当的错误提示和处理
- 代码符合项目规范和最佳实践

## 风险评估

- **中等风险**: 涉及多表操作和复杂业务逻辑
- **关键点**: 事务管理和错误处理
- **缓解措施**: 充分测试、分阶段实现、详细日志

<template>
  <div class="customForm-container" style="height: 100%; overflow: hidden">
    <template v-for="group in visibleAssetFields">
      <div :key="group.formName">
        <div class="my-title">
          <!-- 动态图标渲染 -->
          <img v-if="group.formName === '基本信息'" src="@/assets/images/application/jbxx.png" alt=""/>
          <i v-else-if="group.formName === '硬件/软件概况信息'" class="el-icon-cpu" style="font-size: 24px; margin: 0 5px; color: #4382fd" />
          <i v-else-if="group.formName === '位置信息'" class="el-icon-location-information" style="font-size: 24px; margin: 0 5px; color: #4382fd" />
          <img v-else-if="group.formName === '网络信息'" src="@/assets/images/application/netinfo.png" alt=""/>
          {{ group.formName }}
        </div>

        <!-- 网络信息特殊处理 -->
        <template v-if="group.formName === '网络信息'">
          <el-descriptions
            class="custom-column"
            direction="vertical"
            size="medium"
            :colon="false"
            label-class-name="custom-label-style"
            content-class-name="custom-content-style"
            :column="3">
            <el-descriptions-item label="外网IP">
              <el-col :class="[ !form.exposedIp ? 'empty' : 'collapse-value' ]">
                {{ form.exposedIp || '（空）' }}
              </el-col>
            </el-descriptions-item>
          </el-descriptions>
          <el-table :data="macAipList" :header-cell-style="headerCellStyle" :cell-style="cellStyle">
            <el-table-column label="是否主ip">
              <template slot-scope="scope">
                <span v-if="scope.row.mainIp === '1'">是</span>
                <span v-if="scope.row.mainIp === '0'">否</span>
              </template>
            </el-table-column>
            <el-table-column prop="domainFullName" label="所属网络" />
            <el-table-column prop="ipv4" label="ip" />
            <el-table-column prop="mac" label="mac" />
          </el-table>
        </template>

        <!-- 其他分组正常渲染 -->
        <template v-else>
          <el-descriptions
            class="custom-column"
            direction="vertical"
            size="medium"
            :colon="false"
            label-class-name="custom-label-style"
            content-class-name="custom-content-style"
            :column="3">
            <el-descriptions-item
              v-for="field in group.fieldsItems"
              :key="field.fieldKey"
              :label="field.fieldName">
              <!-- 所属部门特殊处理 -->
              <template v-if="field.fieldKey === 'deptId'">
                <span :class="[ !form.deptName ? 'empty' : 'collapse-value' ]">
                  {{ form.deptName || '（空）' }}
                </span>
              </template>

              <!-- 其他字段正常渲染 -->
              <template v-else>
                <span :class="[ !getFieldValue(field) ? 'empty' : 'collapse-value' ]">
                  {{ getFieldDisplayValue(field) || '（空）' }}
                </span>
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
import assetRegister from "@/mixins/assetRegister";
export default {
  name: 'BasicInfo',
  mixins: [assetRegister],
  dicts: ['impt_grade', 'sys_yes_no', 'proc_type','is_sparing'],
  props: {
    form: {
      type: Object,
      default: null
    },
    edrForm: {
      type: Object,
      default: null
    },
    myTags: {
      type: Array,
      default: null
    },
    macAipList: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      activeNames: ['1', '2', '3', '4'],
      collapseLabelSpan: 4,
      collapseContentSpan: 8,
      headerCellStyle: { 'font-weight': 'normal', color: '#979797' },
      cellStyle: { 'font-weight': 'bold' },
      assetAllocationType: '2',
    }
  },
  computed: {
    visibleAssetFields() {
      return (this.assetList || []).map(group => ({
        ...group,
        fieldsItems: group.fieldsItems.filter(item => item.isShow)
      }));
    }
  },
  mounted() {},
  methods: {
    getFieldValue(field) {
      return this.form[field.fieldKey];
    },
    getFieldDisplayValue(field) {
      const value = this.getFieldValue(field);
      // 特殊字段处理
      if (field.fieldKey === 'degreeImportance') {
        return this.dict.type.impt_grade.find(d => d.value === value)?.label || value;
      }
      if (field.fieldKey === 'isVirtual') {
        return value === 'Y' ? '是' : value === 'N' ? '否' : value;
      }
      if (field.fieldKey === 'isSparing') {
        return this.dict.type.is_sparing.find(d => d.value === value)?.label || value;
      }
      if (field.fieldKey === 'maintainUnit') {
        return this.form.maintainUnitName;
      }
      if (field.fieldKey === 'facilityManufacturer') {
        return this.form.facilityManufacturerName || value;
      }
      if (field.fieldKey === 'vendor') {
        return this.form.vendorName || value;
      }
      if (field.fieldKey === 'assetType') {
        return this.form.assetTypeDesc || value;
      }
      return value;
    }
  },
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/customForm";
  .collapse-title {
    font-weight: bold;
    margin-left: 20px;
  }
  .collapse-row {
    line-height: 32px;
  }
  .collapse-content-div {
    margin: 0 20px;
  }
  .collapse-label {
    color: #979797;
  }
  .collapse-value {
    font-weight: bold;
  }
  .empty {
    color: #979797;
  }
</style>

<template>
  <div>
    <div v-show="!formVisible">
      <div>
        <JNPF-table :has-n-o="false" v-loading="listLoading" :data="list" @sort-change='sortChange' :span-method="arraySpanMethod" height="360px">
          <el-table-column prop="sip" min-width="120" label="攻击源IP" align="left">
          </el-table-column>
          <el-table-column prop="sport" label="源端口" width="90px;" align="left" />
          <el-table-column prop="dip" min-width="120" label="目标IP" align="left">
          </el-table-column>
          <el-table-column prop="dport" label="目标端口" width="90px;" align="left" />
<!--          <el-table-column label="关联业务系统"  prop="businessApplicationList" width="150" :show-overflow-tooltip="false">
            <template slot-scope="scope">
              <el-tooltip placement="bottom-end" effect="light" v-if="scope.row.businessApplications && scope.row.businessApplications.length > 0">
                <div slot="content">
                  <div v-for="(item,tagIndex) in scope.row.businessApplications" :key="item.assetId" class="overflow-tag" v-if="tagIndex <= 5">
                    <el-tag type="primary"><span>{{item.assetName}}</span></el-tag>
                  </div>
                  <div v-if="scope.row.businessApplications.length > 5">
                    <el-tag type="primary"><span>...</span></el-tag>
                  </div>
                </div>
                <el-tag type="primary" class="asset-tag"><span>{{handleApplicationTagShow(scope.row.businessApplications)}}</span></el-tag>
              </el-tooltip>
            </template>
          </el-table-column>-->
          <el-table-column prop="procotol" label="协议" width="90px;" align="left" />
<!--          <el-table-column prop="threatenName" min-width="120" label="告警名称" align="left" />
          <el-table-column prop="threatenType" min-width="120" label="攻击类型" align="left" />-->
          <el-table-column prop="createTime" min-width="120" label="告警时间" align="left" />
          <el-table-column prop="syncStatus" min-width="120" label="同步状态" align="left" />
          <el-table-column label="操作" :show-overflow-tooltip="false" fixed="right" width="120">
            <template slot-scope="scope">
              <el-button type="text" @click="addOrUpdateHandle(scope.row, true)">详情</el-button>
            </template>
          </el-table-column>
        </JNPF-table>
        <pagination :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize" layout="total, prev, pager, next, jumper" @pagination="initData" />
      </div>
    </div>
    <big-form :visible.sync="formVisible" ref="BigForm" @refresh="refresh" />
  </div>
</template>

<script>
  import BigForm from './big-form.vue'
  import {mapGetters} from "vuex";
  import {getList,delData} from '@/api/aqsoc/threaten-web-shell/crud'
  import { getFfsafeFlowDetailList } from '../../../api/threat/threat'

  export default {
    name:'FfsafeFlowDetail',
    components: {BigForm},
    props:{
      view:{
        type: Number,
        require: false,
        default:0
      },
      alarm:{
        type: Object,
        require: false
      },
      showIpTag: {
        type: Boolean,
        require: false,
        default: false
      }
    },
    data() {
      return {
        keyword: '',
        expandObj: {},
        query: {
        },
        list: [],
        listLoading: true,
        formVisible: false,
        total: 0,
        mergeList: [],
        listQuery: {
          // currentPage: 1,
          pageNum: 1,
          pageSize: 10
        },
      }
    },
    computed: {
      ...mapGetters(['userInfo']),
      menuId() {
        return this.$route.meta.modelId || ''
      }
    },
    created() {
      this.initSearchDataAndListData()
    },
    methods: {
      addOrUpdateHandle(row, isDetail, isAudit) {
        this.formVisible = true
        this.$nextTick(() => {
          this.$refs.BigForm.init(row, isDetail, isAudit)
        })
      },
      arraySpanMethod({column}) {
        for (let i = 0; i < this.mergeList.length; i++) {
          if (column.property == this.mergeList[i].prop) {
            return [this.mergeList[i].rowspan, this.mergeList[i].colspan]
          }
        }
      },
      sortChange({column, prop, order}) {
        this.initData()
      },
      async initSearchDataAndListData() {
        this.initData()
      },
      initData() {
        this.listLoading = true;
        let _query = {
          ...this.listQuery,
          ...this.query,
          keyword: this.keyword,
          menuId: this.menuId
        };
        if(this.alarm){
          _query.dip = this.alarm.destIp
          _query.threatenType = this.alarm.threatenType
          _query.threatenName = this.alarm.srcThreatenName
          _query.sip = this.alarm.srcIp;
          _query.dport = this.alarm.destPort;
          _query.dataSource = this.alarm.dataSource;
        }
        getFfsafeFlowDetailList(_query).then(res => {
          this.list = res.rows
          this.total = res.total
          this.listLoading = false
        })
      },
      search() {
        // this.listQuery.currentPage = 1
        this.listQuery.pageNum = 1
        this.listQuery.pageSize = 10
        this.listQuery.sort = "desc"
        this.listQuery.sidx = ""
        this.initData()
      },
      refresh(isrRefresh) {
        this.formVisible = false
        if (isrRefresh) this.reset()
      },
      reset() {
        for (let key in this.query) {
          this.query[key] = undefined
        }
        this.search()
      },
      handleApplicationTagShow(applicationList){
        if(!applicationList || applicationList.length < 1){
          return '';
        }
        let result = applicationList[0].assetName;
        if(applicationList.length > 1){
          result += '...';
        }
        return result;
      },
    }
  }
</script>

<style lang="scss" scoped>
.asset-tag{
  margin-left: 5px;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.overflow-tag:not(:first-child){
  margin-top: 5px;
}
</style>
